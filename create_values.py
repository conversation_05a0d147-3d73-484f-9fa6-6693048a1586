"""
This script although fairly rustic is used to create the values.yaml file for each customer.

The way it works is that it takes the default values.yaml file and merges it with the customer specific values.yaml file inside the folder `default_values`.

The script then creates a new folder for each customer and copies the eyecue helm chart into it. It then dumps the merged values.yaml file into the new folder and creates a helm package for each customer.

Using command line arguments it then pushes the helm package to the correct s3 bucket.

The script is run by a bitbucket pipeline.
"""

import os
import re
import subprocess
import sys
from distutils.dir_util import copy_tree, remove_tree

import yaml
from loguru import logger

from scripts.roler import Boto3STSService

CV_PROD_ACCOUNT_ID = "************"


def run_command(command: str) -> tuple[str, str, int]:
    """
    Runs a command and returns the output.
    """
    result = subprocess.run(
        command.split(" "), stdout=subprocess.PIPE, stderr=subprocess.PIPE
    )
    return (
        result.stdout.decode("utf-8").replace("\\n", "\n").replace("\\t", "\t"),
        result.stderr.decode("utf-8").replace("\\n", "\n").replace("\\t", "\t"),
        result.returncode,
    )


def parse_yaml_file(file: str) -> dict:
    """
    Parses a yaml file and returns the content.
    """
    with open(file) as stream:
        return yaml.safe_load(stream)


def dump_to_yaml(content: dict, customer=None) -> None:
    """
    Dumps a dict to a yaml file. The output file will be named `values.yaml` inside the folder `eyecue-{customer}`.
    """
    with open(f"./eyecue-{customer}/values.yaml", "w") as file:
        yaml.dump(content, file, sort_keys=True)


def extract_docker_images(yaml_string):
    pattern = r'image:\s*["\']?(.*?)["\']?\s*$'
    matches = re.findall(pattern, yaml_string, re.MULTILINE)
    docker_images = [image.strip() for image in matches]
    return docker_images


def dump_list_yaml(list: list, file_name):
    """
    Dumps a list to a yaml file. The output file will be named `values.yaml` inside the folder `eyecue-{customer}`.
    """
    with open(file_name, "w") as file:
        yaml.dump(list, file, sort_keys=True)


def pretty_print_yaml(content: dict) -> None:
    """
    Pretty prints a dict.
    """
    logger.info(yaml.dump(content, sort_keys=True))


def create_helm_package(
    tag: str, customer: str, test_site: str, bucket_name: str
) -> None:
    """
    Creates a helm package and pushes it to the correct s3 bucket.

    The way this function works is that it first adds the s3 bucket as a helm repo.

    Then it renders the helm chart using the test site as the hostname and things name to makre sure the templates are rendered correctly.

    Then it creates a helm package and pushes it to the s3 bucket.

    The helm repo added has the following format: `eyecue-{customer}` and the s3 that it is pushed to: s3://{bucket_name}/eyecue

    Args:
        tag: the tag to be used in the helm package.
        customer: the customer name. (i.e, cfa-us, mcd-nz, etc.)
        test_site: the test site to be used while rendering the helm package, this is used to make sure the templates are rendered correctly.
        bucket_name: the name of the s3 bucket to push the helm package to.
    """
    final_tag = f"{tag}-{customer}"

    out, err, code = run_command(
        f"helm repo add eyecue-{customer} s3://{bucket_name}/eyecue"
    )
    if code != 0:
        logger.error("Failed to render helm chart for customer:", customer)
        logger.error(err)
        sys.exit(code)

    rendering_command = f"helm template ./eyecue-{customer}/ --set global.hostname={test_site} --set global.configuration.site_id={test_site} --set configuration.utc_midnight_offset=0"
    out, err, code = run_command(rendering_command)
    if code != 0:
        logger.error("Failed to render helm chart for customer:", customer)
        logger.error(err)
        sys.exit(code)

    with open(f"output/{final_tag}.yaml", "w") as file:
        file.write(out)

    docker_images = extract_docker_images(out)
    sorted_docker_images = sorted(set(docker_images))
    dump_list_yaml(sorted_docker_images, f"output/{final_tag}-images.yaml")
    logger.info(
        "Docker images used in this helm package for customer:", customer, final_tag
    )
    pretty_print_yaml(sorted_docker_images)
    with open(f"output/{final_tag}.yaml", "w") as file:
        file.write(out)
    run_command(f"helm package eyecue-{customer} --version {final_tag} -d output/")
    run_command(
        f"helm s3 push ./output/eyecue-{final_tag}.tgz eyecue-{customer} --relative"
    )
    logger.success(f"Created helm package for {customer} with tag {final_tag}")


def merge_dict(dict1: dict, dict2: dict) -> dict:
    """
    This function merges two dicts together. If the key is a dict then it will merge the two dicts together. If the key is not a dict then it will replace the value in dict1 with the value in dict2.
    The merging is done in place and the dict1 is returned.
    """
    for key, val in dict1.items():
        if type(val) == dict and type(dict2) == dict:
            if key in dict2 and type(dict2[key] == dict):
                merge_dict(dict1[key], dict2[key])
        else:
            if key in dict2 and type(dict2) == dict:
                dict1[key] = dict2[key]

    for key, val in dict2.items():
        if not key in dict1:
            dict1[key] = val

    return dict1


def merge_two_values(default: dict, template: dict) -> dict:
    """
    Merges a default value with a template value. This function is inplace to get the correct order of merging.
    """
    return merge_dict(template, default)


def update_deployment_params_template_table(
    tag: str,
    client_id: str,
    aws_account_id: str,
    table_name: str = "eyecue-deployment-params-template",
    role: str = "DevAccess",
    region: str = "ap-southeast-2",
):
    """Update the deployment params template table with the new tag.

    This is the table that contains the default helm values for each customer.

    We update the default helm chart tag here so that the latest tag is used
    when a new site gets deployed.

    Args:
        tag (str): The new tag to update the table with. E.g., 1.0.0-mcd-au
        client_id (str): The client id to update. This is the primary key of the table.
        aws_account_id (str): The AWS account id to use.
        table_name (str, optional): The table name to use.
            Defaults to "eyecue-deployment-params-template".
        role (str, optional): The role to assume. Defaults to "DevAccess".
        region (str, optional): The region to use. Defaults to "ap-southeast-2".

    """
    dynamodb = Boto3STSService(
        f"arn:aws:iam::{aws_account_id}:role/{role}"
    ).get_dynamodb_session(region)

    table = dynamodb.Table(table_name)
    response = table.get_item(Key={"client_id": client_id})

    try:
        item = response["Item"]
    except KeyError:
        logger.error(f"Item not found in {table_name} for {client_id}")
        sys.exit(1)

    def get_dependency_by_name(name: str, item: dict) -> dict | None:
        """Get a dependency by name."""
        for dependency in item["dependencies"]:
            if dependency["name"] == name:
                return dependency
        return None

    eyecue = get_dependency_by_name("eyecue", item)
    if eyecue is None:
        logger.error(f"eyecue not found in {table_name} for {client_id}")
        raise sys.exit(1)

    eyecue["version"] = tag
    table.put_item(Item=item)

    logger.success(f"Updated {table_name} for {client_id} with tag {tag}")


def is_valid_env_type(env: str) -> bool:
    if env in ["test", "staging", "prod"]:
        return True
    return False


@logger.catch(reraise=True)
def main(tag, env):
    """
    The main function of the script.
    Receives the tag and the environment as arguments.

    The tag is used to create the helm package and the environment is used to determine which customers to create the helm package for.
    While in test environment only the qa helm package is created.

    This function starts by reading the accounts.yaml file. This file contains the list of customers and the default values.yaml file as well as which bucket to push the helm package to.
    from we create a new folder for each customer and copy the eyecue helm chart into it. Then we merge the default values.yaml file with the customer specific values.yaml file and dump it into the new folder.
    Then we create a helm package for each customer and push it to the correct s3 bucket.

    Env could be "test", "staging" or "prod":
        - "test": generates test tags only to qa accounts.
        - "staging": generates a temporary tags for all customers, but doesn't update the DynamoDB default records. *(Should be used to test critical helm templates changes
            on a few customer sites before whole fleet deployments)
        - "prod": generates the production templates for all customers and updates the DynamoDB default records.

    """
    logger.info("Tagging with:", tag, env)
    if not is_valid_env_type(env):
        raise ValueError(f"The selected env type does not exist: {env}.")
    files = parse_yaml_file("accounts.yaml")
    buckets = files["buckets"]
    os.makedirs("output", exist_ok=True)
    for customer_name, customer_content in files["sites"].items():
        logger.info(f"Creating helm package for {customer_name}")

        template = parse_yaml_file(customer_content["file"])
        default = parse_yaml_file(files["default"])
        new_template = merge_two_values(template, default)
        customer = customer_content["name"]
        copy_tree("eyecue", f"eyecue-{customer}")
        dump_to_yaml(new_template, customer=customer)
        bucket_lookup = customer_content.get("bucket-name", "au-bucket")
        bucket_content = buckets[bucket_lookup]
        bucket_region = bucket_content.get("region", "ap-southeast-2")
        os.environ["AWS_DEFAULT_REGION"] = bucket_region
        bucket_name = bucket_content.get("name", "eyecue-helm-cv-prod-package")
        test_site = customer_content["test-site"]
        create_helm_package(tag, customer, test_site, bucket_name)
        remove_tree(f"eyecue-{customer}")

        if env == "prod":
            # Update the deployment params template table
            client_id = customer_content.get("client_id", customer_name)
            customer_tag = f"{tag}-{customer}"
            update_deployment_params_template_table(
                tag=customer_tag,
                client_id=client_id,
                role=os.environ.get("ROLE_TO_ASSUME", "DevAccess"),
                aws_account_id=CV_PROD_ACCOUNT_ID,
            )


if __name__ == "__main__":
    main(sys.argv[1].replace("test-", ""), sys.argv[2])
