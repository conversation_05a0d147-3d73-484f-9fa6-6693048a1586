image: python:3.11-slim
pipelines:
  default:
    - step:
        name: Check Conventional Commits
        image: commitizen/commitizen:3.18.3
        script:
          - cz check --rev-range HEAD^..HEAD
  custom:
    scheduled-pr:
      - step:
          name: Create PR from dev to master
          image: curlimages/curl:latest
          script:
            # every 1 year you need to renew the api key
            - >
              curl -X POST -u "$BB_API_EMAIL:$BB_API_KEY" \
              https://api.bitbucket.org/2.0/repositories/fingermarkltd/eyecue-helm-templates/pullrequests \
              -H "Content-Type: application/json" \
              -d '{
                "title": "Auto PR: Merge development into master",
                "source": {
                  "branch": {
                    "name": "development"
                  }
                },
                "destination": {
                  "branch": {
                    "name": "master"
                  }
                },
                "description": "Automated PR created by Bitbucket Pipelines every 2 weeks",
                "close_source_branch": false
              }'

    update-version:
      - variables:
          - name: VERSION
            default: "latest" # gets the latest version from pyproject.toml
          - name: CUSTOMER_NAME
            default: "all"
          - name: SYNC_ARGOCD
            default: "false"
            allowed-values:
              - "true"
              - "false"
      - step:
          oidc: true
          name: Update version and sync ArgoCD
          caches:
            - pip
          script:
            - apt update
            - apt install -y git
            - pip install --upgrade pip
            - pip install -r requirements.txt
            - |
              ARGS=""
              if [ "$VERSION" != "latest" ]; then
                ARGS="--version $VERSION"
              fi

              if [ "$CUSTOMER_NAME" != "all" ]; then
                ARGS="$ARGS --customer $CUSTOMER_NAME"
              fi

              if [ "$SYNC_ARGOCD" = "true" ]; then
                ARGS="$ARGS --sync"
              fi
            - python3 scripts/update_version.py $ARGS
    deploy-automatically:
      - step:
          oidc: true
          name: Deploy to customer automatically
          script:
            - apt update
            - apt install -y git
            - pip3 install -U pip
            - pip3 install -r requirements.txt
            - python3 scripts/deploy_canary.py --mode auto
          artifacts:
            - backup/**

    # Updates an image tag in the charts default.yaml.
    # This is triggered from the upstream repo using the pipe 'atlassian/trigger-pipeline'
    # The SRC_* variables are passed in from the upstream repo.
    update-image-tag:
      - variables:
        - name: IMAGE_TAG_PATH
        - name: SRC_BITBUCKET_TAG
        - name: SRC_BITBUCKET_REPO_SLUG
        - name: SRC_BITBUCKET_COMMIT
      - step:
          name: Update image tag in default.yaml
          image: atlassian/default-image:5
          script:
            - curl -sSL --fail https://github.com/mikefarah/yq/releases/latest/download/yq_linux_amd64 -o /usr/local/bin/yq
            - chmod +x /usr/local/bin/yq
            - ./scripts/update-image-tag.sh
            - git add default_values/default.yaml
            - |
              git commit -m \
                "chore: '$IMAGE_TAG_PATH' updated to '$SRC_BITBUCKET_TAG'.

              See commit in $SRC_BITBUCKET_REPO_SLUG:
              https://bitbucket.org/$BITBUCKET_WORKSPACE/$SRC_BITBUCKET_REPO_SLUG/commits/$SRC_BITBUCKET_COMMIT"
            - git push --atomic origin $BITBUCKET_BRANCH

  tags:
    "*.*.*":
      - step:
          name: Update Helm and create values
          oidc: true
          script:
            - apt update
            - apt install -y wget tar git
            # install Helm and update dependencies
            - wget https://get.helm.sh/helm-v3.17.1-linux-amd64.tar.gz
            - tar -zxvf helm-v3.17.1-linux-amd64.tar.gz
            - mv linux-amd64/helm /usr/local/bin/helm
            - helm plugin install https://github.com/hypnoglow/helm-s3.git --version 0.16.0
            - helm dependency update eyecue
            - > # set environment depending on tag format
              if [[ "${BITBUCKET_TAG}" =~ .*-.* ]]; then
                  ENV="test"
                else
                  ENV="prod"
              fi
            - echo "Environment determined to be $ENV based on BITBUCKET_TAG"
            # install and run create-values
            - pip3 install -r requirements.txt
            - python3 create_values.py ${BITBUCKET_TAG} $ENV

  branches:
    master:
      - step:
          image: commitizen/commitizen:3.18.3
          name: Bumping version
          script:
            # skip when bitbucket pipelines pushes stuff, to avoid loops
            - test $BITBUCKET_STEP_TRIGGERER_UUID = {406a3c3c-9e6a-4312-b8c5-5d7af4e86bb3} && echo $BUMP_SKIP_MESSAGE && exit 0
            - apk update && apk add --no-cache git
            - git remote set-url origin https://eyecue-deployer:${APP_SECRET}@bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}
            # remove tags that don't match our format, so that they aren't included in the changelog
            - for tag in $(git tag | grep -v -E '[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$'); do git tag -d $tag; done;
            - cz bump
            - cz changelog
            - "git commit -m 'chore: updating CHANGELOG.md [skip ci]' CHANGELOG.md"
            - git push
            - git push --tags
