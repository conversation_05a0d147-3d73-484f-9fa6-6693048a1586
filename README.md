# EYECue Helm Templates

In this repo we create the helm packages for EYECue. Costumer specific values are found in the folder `default_values/` the default set of configuration is found on `default_values/default.yaml`. This repository combines two values, the customer specific `yaml` and the default `yaml` to generate a final `values.yaml` for the eyecue package.

The build is ran every tag to the repository. It will create a helm package for each customer inside the files `accounts.yaml`. The final tag of the helm chart is: `${bitbucket-tag}-${accounts.yaml::name}`. (Note that helm packages only support SemVer).

I.e if we tag the repo with the tag `0.1.0`, we our `accounts.yaml` contains the following content:

```yaml
sites:
  czp-us:
    file: default_values/czp-us.yaml
    name: czp-us
    test-site: fm-czp-us-023
  mcd-au:
    file: default_values/mcd-au.yaml
    name: mcd-au
    test-site: fm-mcd-aus-023
  qa-au:
    file: default_values/qa-au.yaml
    name: qa-au
    test-site: fm-qa-aus-023
default: default_values/default.yaml
```

After amalgamating the default values with each individual config file, the pipeline will create 3 helm charts: `0.1.0-qa-au`, `0.1.0-mcd-au` and `0.1.0-czp-us`.
The chars are stored and serverd from an s3 bucket in the CV PROD account (`eyecue-helm-cv-prod-package`)

## Testing Helm Templates Locally

To test modifications in helm template charts locally, just follow the steps below:

1. Install helm package following the instructions in this [link](https://helm.sh/docs/intro/install/).

2. Depending of what template you want to test, you may need to add configurations in `default_values/default.yaml` .

   2.1. For example, if you want to test modifications in `templates/tessera/k8s-eyeq-tessera-cronjob.yaml`, you may need to add the following configurations inside the `containers` key in the file":

   ```yaml
   tessera:
   jobs:
     - camera_feed: "rtsp://"
       camera_number: "1"
       seconds: "60"
   ```

3. In the file `create_values.py`, change the function `create_helm_package()` to:

```python
def create_helm_package(tag: str, customer: str, test_site: str, bucket_name: str) -> None:
    final_tag = f"{tag}-{customer}"
    run_command(f"helm repo add eyecue-{customer} s3://{bucket_name}/eyecue")
    rendering_command = f"helm template ./eyecue-{customer}/ --set global.hostname={test_site} --set global.configuration.site_id={test_site} --set configuration.utc_midnight_offset=0 --debug"
    out, code = run_command(rendering_command)
    print(out)
    if code != 0:
        print("Failed to render helm chart for customer:", customer)
        return
    return
```

4. Run the command `python3 create_values.py $TAG test`.

# TO-DO

Revisit later to add infra stuff to its own helm package.
