#!/usr/bin/env sh

set -eu

USAGE="\

Usage: $0 <IMAGE_TAG_PATH> <SRC_BITBUCKET_TAG>

Update the image tag in the default_values/default.yaml file.

Arguments:
  IMAGE_TAG_PATH: The path to the image tag in the YAML file.
  SRC_BITBUCKET_TAG: The source Bitbucket tag to set as the new image tag.

Examples:
  $0 '.containers.eyeq_tracker.image.tag' '1.0.0'

  # alternatively, you can set the environment variables
  IMAGE_TAG_PATH='.containers.eyeq_tracker.image.tag' SRC_BITBUCKET_TAG='1.0.0' $0
"

IMAGE_TAG_PATH="${1:-${IMAGE_TAG_PATH:?$USAGE}}"
SRC_BITBUCKET_TAG="${2:-${SRC_BITBUCKET_TAG:?$USAGE}}"
VALUES_FILE="default_values/default.yaml"

# yq strips the source formatting, so have used the solution here
# https://github.com/mikefarah/yq/issues/515#issuecomment-1113957629
yqblank() {
  yq eval "$1" "$2" | diff -B "$2" - | patch "$2" -
}

tag_exists() {
  yq eval "${IMAGE_TAG_PATH} != null" "${VALUES_FILE}"
}

tag_type() {
  yq eval "${IMAGE_TAG_PATH} | type" "${VALUES_FILE}"
}

invalid_tag() {
  echo "Error: The specified image tag path '${IMAGE_TAG_PATH}' is invalid or does not exist in ${VALUES_FILE}." >&2
  exit 1
}

tag_exists=$(tag_exists) || invalid_tag
if [ "$tag_exists" != "true" ]; then
  echo "Error: The specified image tag path '${IMAGE_TAG_PATH}' does not exist in ${VALUES_FILE}." >&2
  exit 1
fi

tag_type=$(tag_type) || invalid_tag
if [ "$tag_type" != "!!str" ]; then
  echo "Error: The specified image tag path '${IMAGE_TAG_PATH}' is not a string type. Got '${tag_type}' instead." >&2
  exit 1
fi

echo "Updating image tag at path '${IMAGE_TAG_PATH}' to '${SRC_BITBUCKET_TAG}'"
# The (... | select(tag == \"!!merge\")) statement is used to remove the !!merge tag
# See this answer for more details: https://stackoverflow.com/a/71916131
yqblank "\
    $IMAGE_TAG_PATH |= \"$SRC_BITBUCKET_TAG\" |
    (... | select(tag == \"!!merge\")) tag = \"\"" \
  "${VALUES_FILE}"
