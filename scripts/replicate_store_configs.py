"""
<PERSON><PERSON>t to replicate a site configuration from production into one of our test servers on QA account,
it helps when we need to replicate a bug or test stuff and we have to copy every ROI and detector config.

This script was succesfully run using the following dependencies and versions

simplejson==3.19.1
PyYAML==6.0.1
boto3==1.28.5
deepdiff==6.3.1
loguru==0.7.0

Call it from the root directory like so:

python scripts/replicate_store_configs.py cfa-us fm-cfa-usa-01391 fm-core-tst-0002
"""

import argparse
import copy
import re
import simplejson as json
import time
from utils import parse_yaml_file

from boto3.dynamodb.conditions import Attr
from roler import Boto3STSService
from deepdiff import DeepDiff
from loguru import logger


parser = argparse.ArgumentParser(
    description="Script that replicates configs from one production site and overwrites the config for a test site on DynamoDB."
)

parser.add_argument(
    "--dry-run",
    action="store_true",
    default=False,
    required=False,
    help="Enable dry run mode, changes that would be applied will be shown (default: False)",
)
parser.add_argument(
    "account_id",
    type=str,
    choices=[
        "amr-me",
        "czp-us",
        "kfc-au",
        "mcd-au",
        "mcd-nz",
        "cfa-us",
        "qa-au",
        "elj-au",
        "poc",
    ],
    help="The account id from where the store configs come from",
)
parser.add_argument("source_site", type=str, help="Site ID where the configs come from")
parser.add_argument(
    "dest_site", type=str, help="Site ID where the configs will be overwritten"
)

# Parse the command line arguments
args = parser.parse_args()

# Store the arguments in variables
site_1 = args.source_site
site_2 = args.dest_site
acc_id = args.account_id
dry_run = args.dry_run


def dump_json_to_file(data, file_name):
    with open(file_name, "w") as f:
        f.write(json.dumps(data, indent=4))


def get_boto3_instance(credentials):
    account = credentials["account"]
    role = credentials.get("role", "DevAccess")

    boto3_instance = Boto3STSService(f"arn:aws:iam::{account}:role/{role}")

    return boto3_instance


def get_dynamodb_session(boto3_instance, credentials):
    region = credentials["region"]

    return boto3_instance.get_dynamodb_session(region)


def find_dict_in_list(lst, key, value):
    for i, dic in enumerate(lst):
        if dic[key] == value:
            return i
    return -1


def replicate_server_config(source, dest):
    new_config = copy.deepcopy(dest)

    new_config["detector"] = source.get("detector")
    new_config["visual_comparator"] = source.get("visual_comparator")

    return new_config


def replicate_camera_config(source, dest, site_id):
    new_config = copy.deepcopy(dest)

    new_config["rois"] = source.get("rois")
    new_config["detector"] = source["detector"]

    new_camera_id = get_camera_id(new_config["camera_id"])

    new_config["camera"]["s3_path"] = (
        f"eyecue-cv-qa-au-camera-images/{site_id}/{new_camera_id}.jpg"
    )

    new_config["camera"]["name"] = f"{site_id}-{new_camera_id}"

    new_config["camera"]["source"] = f"rtsp://172.20.5.2:8554/{new_camera_id}"

    return new_config


def create_new_camera(camera_config, site_id):
    new_camera_config = copy.deepcopy(camera_config)

    new_camera_id = get_camera_id(camera_config["camera_id"])

    new_camera_config["camera"]["s3_path"] = (
        f"eyecue-cv-qa-au-camera-images/{site_id}/{new_camera_id}.jpg"
    )

    new_camera_config["camera"]["name"] = f"{site_id}-{new_camera_id}"

    new_camera_config["camera"]["source"] = f"rtsp://172.20.5.2:8554/{new_camera_id}"

    return new_camera_config


def get_camera_id(camera_id):
    regex = r"camera\d{1,3}"

    matches = re.finditer(regex, camera_id, re.MULTILINE)

    for matchNum, match in enumerate(matches, start=1):
        return match.group()


def parse_eyecue_version(camera_id):
    regex = r"\d{1,3}\.\d{1,3}\.\d{1,3}"

    matches = re.finditer(regex, camera_id, re.MULTILINE)

    for matchNum, match in enumerate(matches, start=1):
        return match.group()


def get_camera_configs(credentials, site):
    boto3_instance = get_boto3_instance(credentials)

    dynamodb_session = get_dynamodb_session(boto3_instance, credentials)

    table = dynamodb_session.Table("eyecue-things-shadow")

    response = table.scan(FilterExpression=Attr("site_id").eq(site))

    if not response["Items"]:
        raise RuntimeError(
            "No camera info found for the site %s, using account %s"
            % (site, credentials["name"])
        )

    return response["Items"]


def get_helm_values(credentials, site):
    boto3_instance = get_boto3_instance(credentials)

    dynamodb_session = get_dynamodb_session(boto3_instance, credentials)

    table = dynamodb_session.Table("eyecue-helm-values")

    response = table.scan(FilterExpression=Attr("hostname").eq(site))

    if not response["Items"]:
        raise RuntimeError("No camera info found for the site %s" % site)

    return response["Items"][0]


def get_weights_config(credentials, site):
    boto3_instance = get_boto3_instance(credentials)

    dynamodb_session = get_dynamodb_session(boto3_instance, credentials)

    table = dynamodb_session.Table("eyecue-weights")

    response = table.scan(FilterExpression=Attr("site_id").eq(site))

    if not response["Items"]:
        raise RuntimeError("No camera info found for the site %s" % site)

    return response["Items"][0]


def replicate_weights_config(config, credentials, site):
    boto3_instance = get_boto3_instance(credentials)

    dynamodb_session = get_dynamodb_session(boto3_instance, credentials)

    table = dynamodb_session.Table("eyecue-weights")

    test_site_weights = get_weights_config(credentials, site)

    pre_change = copy.deepcopy(test_site_weights)

    test_site_weights["models"] = config["models"]
    test_site_weights["nms"] = config["nms"]
    test_site_weights["gpu_usage"] = config["gpu_usage"]

    diff = DeepDiff(pre_change, test_site_weights, ignore_order=True)

    if diff:
        logger.debug(json.dumps(json.loads(diff.to_json()), indent=4))

        if not dry_run:
            table.put_item(Item=test_site_weights)
            logger.info("changed weights for %s\n" % site)


def replicate_site_configs(configs, credentials, site):
    boto3_instance = get_boto3_instance(credentials)

    dynamodb_session = get_dynamodb_session(boto3_instance, credentials)

    table = dynamodb_session.Table("eyecue-things-shadow")

    test_site_configs = get_camera_configs(credentials, site)

    test_site_configs = sorted(test_site_configs, key=lambda x: x.get("camera_id"))
    # filter server configs
    source_server_config = list(filter(lambda x: "server" in x["camera_id"], configs))[
        0
    ]

    server_config_pre_change = copy.deepcopy(source_server_config)

    test_site_server_config = list(
        filter(lambda x: "server" in x["camera_id"], test_site_configs)
    )[0]

    new_server_config = replicate_server_config(
        server_config_pre_change, test_site_server_config
    )

    server_config_diff = DeepDiff(test_site_server_config, new_server_config)

    if server_config_diff:
        if not dry_run:
            logger.debug("Updating eyecue server configs.")
            table.put_item(Item=item)
            logger.info("changed %s\n" % site)

    for item in configs:
        logger.info("Parsing config %s" % item.get("camera_id"))

        camera_id = get_camera_id(item["camera_id"])

        if not camera_id:
            logger.debug(f"Skipping the config for {item['camera_id']}")

            continue

        # those values always change
        item["site_id"] = site
        new_camera_id = get_camera_id(item["camera_id"])
        item["camera_id"] = f"eyeq-tracker-{site}-{new_camera_id}"

        respective_config = None
        diff = None

        try:
            # get the respective config for the test site using camera_id
            respective_config = list(
                filter(lambda x: camera_id in x["camera_id"], test_site_configs)
            )[0]
        except IndexError:
            pass

        if respective_config:
            pre_change = copy.deepcopy(respective_config)

            item = replicate_camera_config(item, respective_config, site)

            diff = DeepDiff(pre_change, item, ignore_order=True)

            if diff:
                logger.debug(
                    "Camera config diffs: \n %s"
                    % json.dumps(json.loads(diff.to_json()), indent=4)
                )

        # if there isn't a config for the same camera_id, just use the new config
        else:
            logger.debug("No respective config found, using source site config only.")

            item = create_new_camera(item, site)

        if not diff and respective_config:
            logger.warning("no changes")
            continue

        if not dry_run:
            table.put_item(Item=item)
            logger.info("changed %s\n" % site)
        time.sleep(1)


def replicate_helm_values(configs, credentials, site):
    source_site_helm_values = configs

    test_site_helm_values = get_helm_values(credentials, site)

    pre_change = copy.deepcopy(test_site_helm_values)

    source_site_helm_values["global"]["cameras"] = sorted(
        source_site_helm_values["global"]["cameras"], key=lambda x: x["name"]
    )

    test_site_helm_values["global"]["cameras"] = sorted(
        test_site_helm_values["global"]["cameras"], key=lambda x: x["name"]
    )

    test_site_helm_values["global"]["cameras"] = source_site_helm_values["global"][
        "cameras"
    ]

    # copy site eyecue helm version
    source_eyecue_dependency_idx = find_dict_in_list(
        source_site_helm_values["dependencies"], "name", "eyecue"
    )

    eyecue_version = source_site_helm_values["dependencies"][
        source_eyecue_dependency_idx
    ].get("version")

    if eyecue_version:
        new_version = parse_eyecue_version(eyecue_version)

        test_eyecue_dependency_idx = find_dict_in_list(
            source_site_helm_values["dependencies"], "name", "eyecue"
        )

        test_site_helm_values["dependencies"][test_eyecue_dependency_idx]["version"] = (
            f"{new_version}-qa-au"
        )

    else:
        logger.warning("This site does not have eyecue version set.")

    diff = DeepDiff(pre_change, test_site_helm_values, ignore_order=True)

    if diff:
        logger.debug("Helm values diff:\n %s" % json.dumps(diff, indent=4))

        boto3_instance = get_boto3_instance(credentials)

        dynamodb_session = get_dynamodb_session(boto3_instance, credentials)

        table = dynamodb_session.Table("eyecue-helm-values")

        if not dry_run:
            table.put_item(Item=test_site_helm_values)

            logger.info("changed %s\n" % site)
    else:
        logger.debug("no helm values changes to site %s" % site)


def clean_site_config(configs, credentials, site):
    boto3_instance = get_boto3_instance(credentials)

    dynamodb_session = get_dynamodb_session(boto3_instance, credentials)

    table = dynamodb_session.Table("eyecue-things-shadow")

    test_site_configs = get_camera_configs(credentials, site)

    test_site_configs = sorted(test_site_configs, key=lambda x: x.get("camera_id"))

    test_site_camera_set = set(
        [get_camera_id(c["camera_id"]) for c in test_site_configs]
    )

    source_site_camera_set = set([get_camera_id(c["camera_id"]) for c in configs])

    cameras_in_test_but_not_source = test_site_camera_set - source_site_camera_set

    test_server_configs_to_delete = list(
        filter(
            lambda x: any(
                camera_id in x["camera_id"]
                for camera_id in cameras_in_test_but_not_source
            ),
            test_site_configs,
        )
    )

    diff = DeepDiff(test_site_camera_set, source_site_camera_set, ignore_order=True)

    if diff:
        logger.debug(
            "Cameras to be deleted from eyecue-things-shadow:\n %s"
            % json.dumps(json.loads(diff.to_json()), indent=4)
        )

    if not dry_run:
        for dynamo_entry in test_server_configs_to_delete:
            table.delete_item(
                Key={
                    "site_id": dynamo_entry["site_id"],
                    "camera_id": dynamo_entry["camera_id"],
                }
            )

            logger.debug("Deleted camera %s" % dynamo_entry["camera_id"])

            time.sleep(1)


if __name__ == "__main__":
    credentials = parse_yaml_file("accounts.yaml")["sites"][acc_id]

    source_site_configs = get_camera_configs(credentials, site_1)

    logger.debug("%d configs found at site %s " % (len(source_site_configs), site_1))

    source_site_helm_values = get_helm_values(credentials, site_1)

    source_site_weights_config = get_weights_config(credentials, site_1)

    credentials = parse_yaml_file("accounts.yaml")["sites"]["qa-au"]

    source_site_configs = sorted(source_site_configs, key=lambda x: x["camera_id"])

    replicate_site_configs(source_site_configs, credentials, site_2)

    replicate_helm_values(source_site_helm_values, credentials, site_2)

    replicate_weights_config(source_site_weights_config, credentials, site_2)

    clean_site_config(source_site_configs, credentials, site_2)
