import yaml


def paginate_table(table):
    response = table.scan()
    data = response["Items"]
    while data:
        yield from data
        if "LastEvaluatedKey" not in response:
            break
        response = table.scan(ExclusiveStartKey=response["LastEvaluatedKey"])
        data = response["Items"]


def parse_yaml_file(file):
    with open(file) as stream:
        return yaml.safe_load(stream)


def get_launch_darkly_key(file_path):
    with open(file_path, "r") as file:
        return yaml.safe_load(file)["configuration"].get("ld_key")
