import os
import boto3
from boto3.session import Session


class Boto3STSService:
    def __init__(self, arn):
        print("Assuming role", arn)
        is_pipeline = os.environ.get("BITBUCKET_PIPELINE_UUID")
        if is_pipeline is not None:
            sts = boto3.client("sts")
            assume_role_object = sts.assume_role_with_web_identity(
                RoleArn=arn,
                RoleSessionName="updater",
                WebIdentityToken=os.environ["BITBUCKET_STEP_OIDC_TOKEN"],
            )
            # need to assume  role with web identity
        else:
            sess = Session(profile_name="default")
            sts_connection = sess.client("sts")
            assume_role_object = sts_connection.assume_role(
                RoleArn=arn, RoleSessionName="updater", DurationSeconds=3600
            )
        self.credentials = assume_role_object["Credentials"]

    def get_boto3_session(self, region):
        tmp_access_key = self.credentials["AccessKeyId"]
        tmp_secret_key = self.credentials["SecretAccessKey"]
        security_token = self.credentials["SessionToken"]
        boto3_session = Session(
            aws_access_key_id=tmp_access_key,
            aws_secret_access_key=tmp_secret_key,
            aws_session_token=security_token,
            region_name=region,
        )
        return boto3_session

    def get_dynamodb_session(self, region):
        sess = self.get_boto3_session(region=region)
        return sess.resource(service_name="dynamodb")

    def get_s3_session_resource(self, region):
        return boto3.resource(
            "s3",
            region_name=region,
            aws_access_key_id=self.credentials["AccessKeyId"],
            aws_secret_access_key=self.credentials["SecretAccessKey"],
            aws_session_token=self.credentials["SessionToken"],
        )
