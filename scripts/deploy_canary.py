import copy
import os
import sys
import time
from loguru import logger
import simple<PERSON><PERSON> as json
from roler import Boto3STSService
from utils import paginate_table, parse_yaml_file, get_launch_darkly_key
import holidays
from datetime import datetime
import pytz
import toml
import requests
from eyecuecommon.featureflags.launchdarkly_manager import get_flag_content
import argparse


def format_message(site_ids: list[str], version: str, customer: str) -> str:
    site_list = "\n".join(site_ids)
    return f"""
:star: *Version:* {version}
:bust_in_silhouette: *Customer:* {customer}
:globe_with_meridians: *Site IDs:*
```
{site_list}
```
     """


def format_error_message(site_ids: list[str], version: str, customer: str) -> str:
    site_list = "\n".join(site_ids)
    return f"""
:warning: *Error updating machine ids*
:star: *Version:* {version}
:bust_in_silhouette: *Customer:* {customer}
:globe_with_meridians: *Site IDs:*
```
{site_list}
```
"""


def notify_error_slack(
    webhook_url: str, site_ids: list[str], version: str, customer: str
):
    message = format_error_message(site_ids, version, customer)
    post_to_slack(webhook_url, message)


def notify_slack(webhook_url: str, site_ids: list[str], version: str, customer: str):
    message = format_message(site_ids, version, customer)
    post_to_slack(webhook_url, message)


def notify_start_slack(
    webhook_url: str, customer: str, version: str, workday_index: int
):
    message = f"""
:rocket: All sites ids ending with {workday_index} and {workday_index + 5} will be updated
*Starting Deploying Eyecue for {customer} - {version}*
    """
    post_to_slack(webhook_url, message)


def notify_public_holidays_slack(webhook_url: str):
    message = """
:calendar: *Public Holidays in HKB - Skipping update*
"""
    post_to_slack(webhook_url, message)


def get_nzt_workday_index() -> int | None:
    # Define NZT timezone
    nzt = pytz.timezone("Pacific/Auckland")
    nzt_now = datetime.now(nzt)
    weekday = nzt_now.weekday()
    if 0 <= weekday <= 4:
        # Return the index of the workday (0 for Monday, 4 for Friday)
        return weekday
    return None


def post_to_slack(webhook_url: str, message: str):
    headers = {
        "Content-Type": "application/json",
    }
    payload = {
        "text": message,
    }
    response = requests.post(webhook_url, headers=headers, data=json.dumps(payload))

    if response.status_code != 200:
        logger.error(
            f"Request to Slack returned an error {response.status_code}, the response is:\n{response.text}"
        )
    else:
        logger.info("Message posted to Slack successfully")


def check_today_is_nzt_workday() -> bool:
    # Define NZT timezone
    nz_holidays = holidays.country_holidays("NZ", subdiv="HKB")
    nzt = pytz.timezone("Pacific/Auckland")
    if datetime.now(nzt).date() in nz_holidays:
        return False
    return True


def update_eyecue(item, minimal, customer_acronym):
    item["version"] = eyecue_version(minimal["version"], customer_acronym)
    containers = item["variables"].get("containers", {})
    container_copy = copy.deepcopy(containers)
    allowed = ["triton"]
    if customer_acronym == "cfa-us":
        allowed = ["triton"]

    for k, _ in container_copy.items():
        if k not in allowed:
            logger.info(f"    removing {k}")
            del containers[k]
        else:
            logger.info(f"    keeping {k}")


def eyecue_version(version, customer_acronym):
    return f"{version}-{customer_acronym}"


def dump_json_to_file(data, file_name):
    with open(file_name, "w") as f:
        f.write(json.dumps(data, indent=4))


def get_dynamodb_table(credentials: dict[str, str], role_to_assume: str, region: str):
    account = credentials["account"]
    sts_service = Boto3STSService(f"arn:aws:iam::{account}:role/{role_to_assume}")
    dynamodb = sts_service.get_dynamodb_session(region)
    return dynamodb.Table("eyecue-helm-values")


def should_skip_site(
    site_hostname: str, sites: list[str], workday_index: int | None
) -> bool:
    # setting env vars for eyecuecommon.featureflags.launchdarkly_manager
    os.environ["SITE_ID"] = site_hostname
    os.environ["ENVIRONMENT"] = "prod"
    os.environ["CUSTOMER"] = "unknown"
    should_update = get_flag_content(
        "eyecue.deployment.site-deployment-enabled", default=False
    )
    if not should_update:
        logger.info(f"Skipping {site_hostname} due to feature flag evaluation.")
        return True

    if "all" not in sites and site_hostname not in sites:
        return True

    if workday_index is not None:
        site_id = site_hostname.split("-")[-1]
        try:
            if int(site_id) % 5 != workday_index:
                logger.info(f"Skipping {site_hostname} as it is not its workday")
                return True
        except ValueError:
            return True

    return False


def update_dependencies(item: dict, minimal: dict[str, dict], customer_acronym: str):
    new_version_functions = {
        "eyecue": update_eyecue,
    }

    for dependency, update_func in new_version_functions.items():
        if dependency not in minimal:
            continue

        dependency_updated = False
        for dep in item.get("dependencies", []):
            if dep.get("name") == dependency:
                update_func(dep, minimal[dependency], customer_acronym)
                dependency_updated = True
                break

        if not dependency_updated:
            item.setdefault("dependencies", []).append(minimal[dependency])


def update_site_item(item: dict, customer_acronym: str, minimal: dict[str, dict]):
    site_hostname = item["global"]["hostname"]
    pre_change = copy.deepcopy(item)

    item["global"]["configuration"]["things_name"] = site_hostname
    item["global"]["configuration"]["site_id"] = item["global"]["configuration"].get(
        "site_id", site_hostname
    )

    update_dependencies(item, minimal, customer_acronym)

    if pre_change == item:
        logger.info("No changes made")
        return None

    os.makedirs("backup", exist_ok=True)
    dump_json_to_file(pre_change, f"backup/pre_change-{site_hostname}.json")

    return item


def update_site(
    credentials: dict[str, str],
    sites: list[str],
    minimal: dict[str, dict],
    workday_index: int | None = None,
) -> tuple[list[str], list[str]]:
    role_to_assume = os.environ.get("ROLE_TO_ASSUME", "DevAccess")
    region = credentials["region"]
    table = get_dynamodb_table(credentials, role_to_assume, region)
    updated_sites = []
    errors = []
    for item in paginate_table(table):
        try:
            site_hostname = item["global"]["hostname"]
            if should_skip_site(site_hostname, sites, workday_index):
                continue
            logger.info(f"Updating {site_hostname}")
            updated_item = update_site_item(item, credentials["name"], minimal)
            updated_sites.append(site_hostname)
            if updated_item is None:
                continue

            table.put_item(Item=updated_item)
        except Exception as e:
            logger.error(f"Error updating machine id: {item['machine_id']}: {e}")
            logger.exception(e)
            errors.append(item["machine_id"])
    return updated_sites, errors


def load_toml_version():
    toml_file = "pyproject.toml"
    with open(toml_file, "r") as f:
        data = toml.load(f)
    return data["tool"]["commitizen"]["version"]


def automatic():
    slack_webhook = os.environ.get("SLACK_WEBHOOK")
    if slack_webhook is None:
        logger.error("SLACK_WEBHOOK env variable is not set")
        exit()
    is_it_workday = check_today_is_nzt_workday()
    if is_it_workday is False:
        notify_public_holidays_slack(slack_webhook)
        exit()
    workday_index = get_nzt_workday_index()
    if workday_index is None:
        exit()

    minimal = parse_yaml_file("scripts/minimal.yaml")
    credentials = parse_yaml_file("accounts.yaml")
    version = load_toml_version()
    logger.info(f"Version: {version}")
    minimal["eyecue"]["version"] = version
    ld_key = get_launch_darkly_key("default_values/default.yaml")
    os.environ["LAUNCHDARKLY_KEY"] = ld_key

    for k in credentials["sites"]:
        notify_start_slack(slack_webhook, k, version, workday_index)

        logger.info(f"Starting {k}")

        updated_sites, errors = update_site(
            credentials["sites"][k], ["all"], minimal, workday_index
        )
        if errors:
            errors.sort()
            notify_error_slack(
                slack_webhook,
                errors,
                version,
                k,
            )
        if updated_sites:
            updated_sites.sort()
            notify_slack(
                slack_webhook,
                updated_sites,
                version,
                k,
            )


def main(args):
    customer = ""
    minimal = parse_yaml_file("scripts/minimal.yaml")
    credentials = parse_yaml_file("accounts.yaml")
    to_update = parse_yaml_file("scripts/canary.yaml")
    ld_key = get_launch_darkly_key("default_values/default.yaml")
    os.environ["LAUNCHDARKLY_KEY"] = ld_key

    if args.customer != "" and args.version:
        customer = args.customer
        version = args.version
        minimal["eyecue"]["version"] = version
        if customer == "all":
            for k in credentials["sites"]:
                logger.info(f"Starting {k}")
                update_site(credentials["sites"][k], ["all"], minimal)
            exit()
        if customer not in credentials["sites"]:
            logger.info("customer not found in accounts.yaml")
        update_site(credentials["sites"][customer], ["all"], minimal)
        exit()
    for k in to_update:
        update_site(credentials["sites"][k], to_update[k], minimal)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Canary Deployment.")
    parser.add_argument(
        "--mode",
        type=str,
        default="manual",
        choices=["auto", "manual"],
        help="Mode of operation",
    )
    parser.add_argument(
        "--customer", type=str, default="", help="Customer name or identifier"
    )
    parser.add_argument("--version", type=str, help="Version number or identifier")
    args = parser.parse_args()

    if args.mode == "auto":
        automatic()
    else:
        main(args)
