import argparse
import json
import os
import time
from http import HTTPStatus
from pathlib import Path
from typing import TYPE_CHECKING, TypedDict

import boto3
import toml
import yaml
from loguru import logger
from mypy_boto3_lambda import LambdaClient

if TYPE_CHECKING:
    from mypy_boto3_sts.type_defs import (
        AssumeRoleResponseTypeDef,
        AssumeRoleWithWebIdentityResponseTypeDef,
    )


class Customer(TypedDict):
    name: str
    account: str
    region: str


def assume_role_session(
    role_arn: str,
    region_name: str = "ap-southeast-2",
    session: boto3.Session | None = None,
) -> boto3.Session:
    session = session or boto3.Session()
    sts_client = session.client("sts")

    assumed_role: AssumeRoleResponseTypeDef | AssumeRoleWithWebIdentityResponseTypeDef
    if os.getenv("CI") == "true":
        logger.info(
            f"Assuming role: {role_arn} with web identity in region: {region_name}"
        )
        assumed_role = sts_client.assume_role_with_web_identity(
            RoleArn=role_arn,
            RoleSessionName="eyecue-helm-templates-update-version",
            WebIdentityToken=os.environ["BITBUCKET_STEP_OIDC_TOKEN"],
        )
    else:
        logger.info(f"Assuming role: {role_arn} in region: {region_name}")
        assumed_role = sts_client.assume_role(
            RoleArn=role_arn,
            RoleSessionName="eyecue-helm-templates-update-version",
        )

    credentials = assumed_role["Credentials"]
    return boto3.Session(
        aws_access_key_id=credentials["AccessKeyId"],
        aws_secret_access_key=credentials["SecretAccessKey"],
        aws_session_token=credentials["SessionToken"],
        region_name=region_name,
    )


def find_lambda_function(lambda_client: LambdaClient, prefix: str, suffix: str) -> str:
    """Find a Lambda function by its prefix and suffix.

    Workaround for the fact that the lambdas have a dynamic name that includes the
    environment name in the middle e.g: eyecue-helm-parser-dev-updateDependencyVersion
    """
    logger.debug(
        f"Searching for Lambda function with prefix '{prefix}' and suffix '{suffix}'"
    )
    paginator = lambda_client.get_paginator("list_functions")

    for page in paginator.paginate():
        for fn in page["Functions"]:
            name = fn["FunctionName"]
            if name.startswith(prefix) and name.endswith(suffix):
                logger.debug(f"Found Lambda function: {name}")
                return name  # return first match

    raise ValueError(
        f"Lambda function with prefix '{prefix}' and suffix '{suffix}' not found."
    )


def get_customers() -> dict[str, Customer]:
    accounts_yaml_path = Path(__file__).parent.parent / "accounts.yaml"
    logger.info(f"Loading customer accounts from {accounts_yaml_path}")

    with Path(accounts_yaml_path).open("r", encoding="utf-8") as file:
        data = yaml.safe_load(file)

    return data["sites"]  # type: ignore[no-any-return]


def update_version(
    lambda_client: LambdaClient,
    version: str,
    dependency_name: str = "eyecue",
) -> None:
    payload = {
        "version": version,
        "dependency_name": dependency_name,
    }

    function_name = find_lambda_function(
        lambda_client=lambda_client,
        prefix="eyecue-helm-parser",
        suffix="updateDependencyVersion",
    )

    logger.info(f"Updating version for {dependency_name} to {version}")
    response = lambda_client.invoke(
        FunctionName=function_name,
        InvocationType="RequestResponse",
        Payload=json.dumps(payload).encode("utf-8"),
    )

    if response["StatusCode"] != HTTPStatus.OK:
        raise RuntimeError(
            f"Failed to update version for {dependency_name}. "
            f"Lambda response: {json.dumps(response, indent=2)}"
        )


def sync_argocd(lambda_client: LambdaClient) -> None:
    payload = {"application": "eyecue"}

    function_name = find_lambda_function(
        lambda_client=lambda_client,
        prefix="eyecue-helm-parser",
        suffix="argocdSyncAll",
    )

    logger.info("Syncing all ArgoCD applications")
    response = lambda_client.invoke(
        FunctionName=function_name,
        InvocationType="RequestResponse",
        Payload=json.dumps(payload).encode("utf-8"),
    )

    if response["StatusCode"] != HTTPStatus.OK:
        raise RuntimeError(
            "Failed to sync ArgoCD applications. "
            f"Lambda response: {json.dumps(response, indent=2)}"
        )


def load_toml_version() -> str:
    toml_file = Path(__file__).parent.parent / "pyproject.toml"
    with toml_file.open("r", encoding="utf-8") as f:
        data = toml.load(f)

    return data["tool"]["commitizen"]["version"]  # type: ignore[no-any-return]


def parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(description="Update Eyecue Helm Templates")
    parser.add_argument(
        "--version",
        type=str,
        default=load_toml_version(),
        help=(
            "Version to update to, e.g., 1.0.0. "
            "Defaults to the version in pyproject.toml."
        ),
    )
    parser.add_argument(
        "--sync",
        action="store_true",
        help="Sync ArgoCD applications after update.",
    )
    parser.add_argument(
        "--customer",
        type=str,
        help="Customer name to update, e.g., dev-nzl. Defaults to all customers.",
    )
    parser.add_argument(
        "--role-name",
        type=str,
        default=os.getenv("ROLE_TO_ASSUME", "AdminAccess"),
        help=(
            "IAM role name to assume for the customer account. "
            "Defaults to 'AdminAccess'."
        ),
    )
    return parser.parse_args()


def main() -> None:
    args = parse_args()
    for customer in get_customers().values():
        name = customer["name"]
        account_id = customer["account"]
        region = customer["region"]

        if args.customer and name != args.customer:
            logger.debug(
                f"Skipping {name} ({account_id}) as it does not "
                f"match --customer {args.customer}"
            )
            continue

        logger.info(f"Updating {name} ({account_id}) to version {args.version}")
        lambda_client = assume_role_session(
            role_arn=f"arn:aws:iam::{account_id}:role/{args.role_name}",
            region_name=region,
        ).client("lambda")

        update_version(
            lambda_client=lambda_client,
            version=f"{args.version}-{name}",
        )

        if args.sync:
            logger.info("Sleeping for 15 seconds prior to syncing ArgoCD")
            time.sleep(15)
            sync_argocd(lambda_client=lambda_client)
        else:
            logger.info("Skipping ArgoCD sync as --sync is not set")

        logger.success(f"Update for {name} ({account_id}) completed.")


if __name__ == "__main__":
    main()
