import copy
import time

from roler import Boto3STSService
from schema import And, <PERSON><PERSON><PERSON>, SchemaError, Use
from utils import paginate_table, parse_yaml_file


def check(conf_schema, conf):
    try:
        conf_schema.validate(conf)
        return True
    except SchemaError:
        return False


def check_extra_keys(item):
    conf_schema = Schema(
        {
            "configuration": {
                "data_capture": {"restaurant": And(Use(str))},
                "things_name": And(Use(str)),
                "utc_midnight_offset": And(Use(float)),
            },
            "containers": {
                "mosaic": {
                    "dashboard_password": And(Use(str)),
                    "dashboard_username": And(Use(str)),
                }
            },
        }
    )
    keys = {"eyecue": conf_schema}
    for dependency in item.get("dependencies", []):
        name = dependency["name"]
        if name not in keys:
            continue
        valid = check(keys[name], dependency["variables"])
        if valid is False:
            return False
    return True


def update_item(item, new_version):
    for dependency in item.get("dependencies", []):
        dependency["version"] = new_version.get(
            dependency["name"], dependency["version"]
        )
    return item


def update_site(site):
    x = 1
    account = site["account"]
    region = site["region"]
    customer_acronym = v["name"]
    new_name = f"3.7.0-rc3-{customer_acronym}"
    credentials = Boto3STSService(f"arn:aws:iam::{account}:role/AdminAccess")
    dynamodb = credentials.get_dynamodb_session(region)
    table = dynamodb.Table("eyecue-helm-values")
    new_version = {"eyecue": new_name}
    wrong_sites = []
    for item in paginate_table(table):
        item["hostname"] = item["global"]["hostname"]
        # if item["global"]["hostname"] not in site_to_update:
        #      continue
        pre_change = copy.deepcopy(item)
        print("Updating:", item["global"]["hostname"], x)
        extra = check_extra_keys(item)
        if extra is False:
            print(item)
            wrong_sites.append(item["machine_id"])
        resp = update_item(item, new_version)
        if pre_change == resp:
            print("    no changes")
            continue
        x += 1
        print(item["hostname"])
        table.put_item(Item=item)
        time.sleep(5)
        # exit()
    print(wrong_sites)


if __name__ == "__main__":
    files = parse_yaml_file("accounts.yaml")
    to_update = [
        # "mcd-au",
        # "amr-me",
        # "czp-us",
        # "kfc-au",
        # "mcd-nz",
        "qa-au",
        # "poc-au",
    ]
    for k in to_update:
        v = files["sites"][k]
        update_site(v)
