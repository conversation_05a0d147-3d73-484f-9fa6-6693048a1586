import os
import argparse
import paramiko
import subprocess
import threading
import time
import shutil
from minio import Minio
from pymongo import MongoClient
from datetime import datetime, timedelta
from sys import exit


def download_from_minio(minio_client, bucket_name, object_path, local_path):
    try:
        minio_client.fget_object(bucket_name, object_path, local_path)

        print(f"Downloaded {object_path} from MinIO to {local_path}")

    except Exception as e:
        print(f"Error downloading from minio: {e}")


def run_bash_command(command):
    try:
        # Run the Bash command
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
        )

        # Print the command output
        print("Command Output:")
        print(result.stdout)

    except subprocess.CalledProcessError as e:
        # Handle errors if the command fails
        print(f"Error: {e}")
        print("Command Error Output:")
        print(e.stderr)


def run_remote_port_forward(server, username):
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

    try:
        # Connect to the server
        ssh.connect(server, username=username)
        print("Connected to the server")

        # Run kubectl port-forward in the background
        command = "kubectl --kubeconfig /etc/kubernetes/admin.conf port-forward --address 0.0.0.0 -n infra deploy/minio-deployment-infra 40991 9000"
        port_forward_process = ssh.exec_command(command, get_pty=True)

        # Wait for port-forwarding to start (adjust sleep time as needed)
        time.sleep(2)

        # Keep the port-forwarding process running in a separate thread
        threading.Thread(
            target=keep_process_alive, args=(ssh, port_forward_process)
        ).start()

    except Exception as e:
        print(f"Error connecting to the server: {e}")
        ssh.close()


def keep_process_alive(ssh, process):
    try:
        # Wait for the process to finish
        exit_status = process[1].channel.recv_exit_status()
        if exit_status != 0:
            print(f"Port-forwarding process failed with exit status {exit_status}")
    finally:
        # Close the SSH connection when the process finishes
        ssh.close()


def run_local_commands():
    # Execute other Python commands locally while port-forwarding is active
    print("Executing local command 1")
    subprocess.run(["python", "--version"], check=True)

    print("Executing local command 2")
    subprocess.run(["ls", "-l"], check=True)


# example of command:
# python3 get_reid_images_from_production.py --hostname fm-cfa-usa-03964 --username gabriel --output /media/fingermark/hd/analysis/cfa_reid/ --days 1 --candidates 3 --queries 5 --roi F --onlywithmatches


def main():
    parser = argparse.ArgumentParser(
        description="Download best-shot images from production environments (queries, matches and candidates)."
    )
    parser.add_argument(
        "--hostname", type=str, required=True, help="Hostname of the server."
    )
    parser.add_argument(
        "--username", type=str, required=True, help="Username to connect to the server."
    )
    parser.add_argument(
        "--output",
        type=str,
        required=True,
        help="Output folder path to save best-shots.",
    )
    parser.add_argument(
        "--days",
        type=int,
        required=True,
        help="How many days to consider (calculated from datetime.now()).",
    )
    parser.add_argument(
        "--candidates",
        type=int,
        required=True,
        help="Minimum number of candidates to consider while capturing queries.",
    )
    parser.add_argument(
        "--queries",
        type=int,
        required=True,
        help="How many (complete) queries to download.",
    )
    parser.add_argument(
        "--roi",
        type=str,
        required=False,
        default="all",
        help="Name of the ROI that the queries come from.",
    )
    parser.add_argument("--onlywithmatches", action="store_true")

    args = parser.parse_args()

    # Replace these with your actual server details and port-forwarding settings
    server_address = args.hostname
    username = args.username
    output_path = args.output
    total_days_to_consider = args.days
    min_candidates = args.candidates
    total_queries_to_download = args.queries
    roi_to_evaluate_queries_from = args.roi
    save_only_with_matches = args.onlywithmatches
    minio_endpoint = f"{server_address}:9000"
    mongodb_uri = f"mongodb://{server_address}:32000"
    current_datetime = datetime.now()

    # Run the remote port-forwarding command in the background
    run_remote_port_forward(server_address, username)

    print("Creating minio client")
    minio_client = Minio(
        minio_endpoint,
        access_key=server_address,
        secret_key=server_address,
        secure=False,
    )  # Set to True for secure (HTTPS) connection
    print("Minio client created")

    print("Connecting to mongodb")
    client = MongoClient(mongodb_uri)
    print("Connected to mongodb")

    db = client["dev"]

    collection_image_info = db["image_info_document"]
    collection_reid_metrics = db["reid_metrics_vehicle"]

    if roi_to_evaluate_queries_from == "all":
        best_shot_file_info = collection_image_info.find({"image_source": "best_shot"})
    else:
        best_shot_file_info = collection_image_info.find(
            {"image_source": "best_shot", "roi_id": roi_to_evaluate_queries_from}
        )

    counter = 0
    for document in best_shot_file_info:
        if counter >= total_queries_to_download:
            exit()
        if (current_datetime - document["event_time"]) < timedelta(
            days=total_days_to_consider
        ):
            minio_path = document["file_name"]
            reid_candidates_info = collection_reid_metrics.find_one(
                {"tracker_id_query": document["tracker_id"]}
            )
            out_query_path = os.path.join(
                output_path,
                server_address,
                str(counter),
                f"query___{reid_candidates_info['creation_date'].strftime('%Y-%m-%d')}___{reid_candidates_info['creation_date'].hour}___{reid_candidates_info['roi_id_query']}___{reid_candidates_info['tracker_id_query']}.jpg",
            )

            candidates = []
            downloaded_all = True
            if not os.path.exists(
                os.path.join(output_path, server_address, str(counter))
            ):
                os.makedirs(os.path.join(output_path, server_address, str(counter)))

            if len(reid_candidates_info["candidates"]) > min_candidates:
                min_score = 99999999
                for candidate in reid_candidates_info["candidates"]:
                    if (
                        candidate["visual_distance"] < min_score
                        and not candidate["filtered_out"]
                    ):
                        min_score = candidate["visual_distance"]

                if save_only_with_matches and min_score == 99999999:
                    continue

                for candidate in reid_candidates_info["candidates"]:
                    candidate_image_info = collection_image_info.find_one(
                        {
                            "tracker_id": candidate["tracker_id"],
                            "roi_id": candidate["roi_id"],
                        }
                    )
                    if candidate_image_info:
                        if candidate["visual_distance"] != min_score:
                            candidates.append(
                                (
                                    candidate_image_info["file_name"],
                                    os.path.join(
                                        output_path,
                                        server_address,
                                        str(counter),
                                        f"candidate___{candidate_image_info['event_time'].strftime('%Y-%m-%d')}___{candidate_image_info['event_time'].hour}___{candidate_image_info['roi_id']}___{candidate_image_info['tracker_id']}.jpg",
                                    ),
                                )
                            )
                        else:
                            candidates.append(
                                (
                                    candidate_image_info["file_name"],
                                    os.path.join(
                                        output_path,
                                        server_address,
                                        str(counter),
                                        f"match___{candidate_image_info['event_time'].strftime('%Y-%m-%d')}___{candidate_image_info['event_time'].hour}___{candidate_image_info['roi_id']}___{candidate_image_info['tracker_id']}.jpg",
                                    ),
                                )
                            )

                print(
                    f"Downloading images for query {reid_candidates_info['tracker_id_query']}"
                )
                try:
                    download_from_minio(
                        minio_client, "best-shots", minio_path, out_query_path
                    )
                except:
                    downloaded_all = False
                    print("Error downloading query")

                for candidate in candidates:
                    try:
                        download_from_minio(
                            minio_client, "best-shots", candidate[0], candidate[1]
                        )
                    except:
                        downloaded_all = False
                        print("Error downloading candidate")

                if downloaded_all == False:
                    shutil.rmtree(
                        os.path.join(output_path, server_address, str(counter))
                    )
                else:
                    counter += 1
                    print(
                        f"Downloaded images for query {reid_candidates_info['tracker_id_query']}. Done [{counter}/{total_queries_to_download}]"
                    )


if __name__ == "__main__":
    main()
