configuration:
  aws_region: ap-southeast-2
  customer: poc-aus
  account_id: "************"
  images_bucket: eyecue-poc-au-images/new_images

containers:
  mosaic:
    bucket: eyecue-poc-au-mosaic
  camera_displacement:
    images_bucket_captured: eyecue-poc-au-images
    images_bucket_config: eyecue-poc-au-camera-images
    aws_sqs_queue_name: eyecue-camera-displacement-sqs-poc-aus

credentials:
  eyecue_server:
    aws_access_key_id: ****************************
    aws_default_region: YXAtc291dGhlYXN0LTI=
    aws_secret_access_key: b0hzSStWUjhFcVAwalJocC9tUS9OSUVIay8rT254VHFQRTJ2bitsUA==
  image_sync_s3:
    aws_access_key_id: ****************************
    aws_default_region: YXAtc291dGhlYXN0LTI=
    aws_secret_access_key: ODBLc1UrOGNRYlI3dE90d2J0dlNnQ2JWeWFuK3N4OVRWc2hjT3psZQ==
  iot:
    aws_access_key_id: ****************************
    aws_default_region: YXAtc291dGhlYXN0LTI=
    aws_secret_access_key: UHh5dTVQenRRMXU4UDZLcGF0b3NZekhDSStzaGs0L1BnT0YzbVE3NQ==
    endpoint: YTF2ZnZ0dzh4em5jdHotYXRzLmlvdC5hcC1zb3V0aGVhc3QtMi5hbWF6b25hd3MuY29t
  mosaic:
    aws_access_key_id: ****************************
    aws_default_region: YXAtc291dGhlYXN0LTI=
    aws_secret_access_key: bHRZTEhqbURrbUdycFBHYXorRFhjK0NTVnNuZzNNbm1MMHlVQUZ5Yw==
  weights:
    weights_update_aws_access_key_id: ****************************
    weights_update_aws_default_region: YXAtc291dGhlYXN0LTI=
    weights_update_aws_secret_access_key: bUNpb1V4R3pQN0NDZmxtcHJiQ05kT2Fsa2ZFZDlGUE03MzhiL2pwMQ==
  self_signed_ssl_certificate:
    ca_cert: 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
    cert_sn: "1658440102"
    server_cert: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUQ1VENDQXMyZ0F3SUJBZ0lVTmZZWjQ5WVVzMnRiV1J4ZXR4NzVsNndBWGtBd0RRWUpLb1pJaHZjTkFRRUwKQlFBd2JERUxNQWtHQTFVRUJoTUNUbG94RWpBUUJnTlZCQWdNQ1VoaGQydGxjMEpoZVRFV01CUUdBMVVFQnd3TgpTR0YyWld4dlkydE9iM0owYURFVE1CRUdBMVVFQ2d3S1JtbHVaMlZ5YldGeWF6RWNNQm9HQTFVRUF3d1RaWGxsClkzVmxaR0Z6YUdKdllYSmtMbU52YlRBZUZ3MHlNakEzTWpFeU1UUTRNakphRncwek1qQTNNVGd5TVRRNE1qSmEKTUd3eEN6QUpCZ05WQkFZVEFrNWFNUkl3RUFZRFZRUUlEQWxJWVhkclpYTkNZWGt4RmpBVUJnTlZCQWNNRFVoaApkbVZzYjJOclRtOXlkR2d4RXpBUkJnTlZCQW9NQ2tacGJtZGxjbTFoY21zeEhEQWFCZ05WQkFNTUUyVjVaV04xClpXUmhjMmhpYjJGeVpDNWpiMjB3Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLQW9JQkFRRHMKNW9tZ2ZhRE9mY3lPWWFjSkxZdnMyTGgveDMxUDVVdnpUR2F0UGJudkkzMWJEM2hUTVAvN0NWMG9ZQkFrNzhzaApMN3JwMW9jNmgvL1RIUW5uQmtYRkdJa2ttQi9lTVR0TDVKbTNTRWZaM21KdE5UM2ZiWTVLeUVOc0lyeUFZdm1hCjVwdUFVQUc1VVZjTmI2aHp0RUdlU2k2OVVlNXE1am0wSGhlNFRvZmFNWWU0U1VHcE1SM1ZkSXZHZnhHcEZhTnQKV3RxT3RXUjg1bE02aGdPYitMa05IZUFqZWdPVHVSbVpINkRJYTdJaDltSmUyZFRkKzlMVlpQdnFCNExlcEc5YQoybmFWNVBpT0t4WlBUVlJEK2pOL0RqdnVOeDFVcGY4N09oYStaMmFVQTQzT1JnWXV6T0ZLUm52ZERmQldxUG9rCjlsd3lselArV1RuL0FieDlxbjNKQWdNQkFBR2pmekI5TUhzR0ExVWRFUVIwTUhLQ0UyVjVaV04xWldSaGMyaGkKYjJGeVpDNWpiMjJDRjNkM2R5NWxlV1ZqZFdWa1lYTm9ZbTloY21RdVkyOXRnaEowWlhOMExuSmhkR0YwYjNOcgpjaTU0ZVhxQ0VXUmxkaTV5WVhSaGRHOXphM0l1ZUhsNmdnbHNiMk5oYkdodmMzU0hCS3dVQUFHSEJBb0FBQkdICkJBcW9lQjR3RFFZSktvWklodmNOQVFFTEJRQURnZ0VCQU94L2xXVktPaTAwMy96L3lIQVZieDc2TjZTNXEvTEYKdUE2czlhbEVJK1VJWVZCb0ptdWU3UllleWJSNm5LaU04cmltMHNlOGdnRTBXSGx2emRZenRyQTZId0ZaclFINwpQbmZEU3BZUDFIcGdoWWt6TmlnZmdCaERpeUtmZzBhL2g3UVNpbzJma0RRMWlDRUJtUnduOVJDTHhpSXc5bHZuCllTS0pRRm9ybURPVzV6RDg0cHNrcXhBUUdPQUNxWFRuUEVZQ1ZncDRCYjNCaDllMGVTQ2Zwb2J5WStHaVFLbm8KM2dTTURqbDlHcmhqZ25DQW8wYXpBVUdlT04vQ0JtNmw0KzlrRGNpU1RlQlVPSE40TTJtR3g3eDNKbmpGVlNCYQpoK3pXSXU5RkFtUEFxY2MwaVdYcXNOTzFPS2VPcXBUL1RrcDN4emZ5SE9XMERGTlhHSlV2UjFVPQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
    server_key: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  roi_suggestor_sqs:
    aws_access_key_id: ****************************
    aws_secret_access_key: czFQSm1iOGQxeStIMCttam9iTUhodXIzV2s2WjBjVHk5SVFUNU9SLw==
  camera_metrics_exporter:
    aws_access_key_id: ********************
    aws_secret_access_key: RPLiSCU7OElHkpUrw+IDPrEHsHCtezN+baDThrt0
    aws_default_region: ap-southeast-2
