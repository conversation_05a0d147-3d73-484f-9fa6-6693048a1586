configuration:
  aws_region: ap-southeast-2
  customer: kfc-aus
  account_id: "************"
  images_bucket: eyecue-kfc-images/new_images

containers:
  nginx:
    enabled: true
  mosaic:
    bucket: eyecue-kfc-au-mosaic
  camera_displacement:
    images_bucket_captured: eyecue-kfc-images
    images_bucket_config: eyecue-kfc-aus-camera-images
    aws_sqs_queue_name: eyecue-camera-displacement-sqs-kfc-aus

credentials:
  eyecue_server:
    aws_access_key_id: ****************************
    aws_default_region: YXAtc291dGhlYXN0LTI=
    aws_secret_access_key: Rkx1QnNITFlPR3lMUzNnalR0UHN5VzV0WDh4Y0dVWE41MjZrSGRXTw==
  image_sync_s3:
    aws_access_key_id: ****************************
    aws_default_region: YXAtc291dGhlYXN0LTI=
    aws_secret_access_key: SmVkMk5wVlg5YWNkUGFMblcxWENzU2NpUDZkaDBXUGtXMXIrcFlzOA==
  iot:
    aws_access_key_id: ****************************
    aws_default_region: YXAtc291dGhlYXN0LTI=
    aws_secret_access_key: czdYZkdtL2JvRDVUcHF3K09nYisyUkNiSnBreEpidVpzUWE3cG9vRw==
    endpoint: YTI5YmhwanI1M2kxNjMtYXRzLmlvdC5hcC1zb3V0aGVhc3QtMi5hbWF6b25hd3MuY29t
  mosaic:
    aws_access_key_id: ****************************
    aws_default_region: YXAtc291dGhlYXN0LTI=
    aws_secret_access_key: TzNtQ1B6MHZDYkVLVUtVSDUrb01nR0J0MFRDMGhycWxXcDhVbnl6aA==
  self_signed_ssl_certificate:
    ca_cert: 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
    cert_sn: "1646168391"
    server_cert: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUQ0ekNDQXN1Z0F3SUJBZ0lKQUxXNHlTYVFOREMrTUEwR0NTcUdTSWIzRFFFQkN3VUFNR3d4Q3pBSkJnTlYKQkFZVEFrNWFNUkl3RUFZRFZRUUlEQWxJWVhkclpYTkNZWGt4RmpBVUJnTlZCQWNNRFVoaGRtVnNiMk5yVG05eQpkR2d4RXpBUkJnTlZCQW9NQ2tacGJtZGxjbTFoY21zeEhEQWFCZ05WQkFNTUUyVjVaV04xWldSaGMyaGliMkZ5ClpDNWpiMjB3SGhjTk1qSXdNekF4TWpBMU9UVXhXaGNOTXpJd01qSTNNakExT1RVeFdqQnNNUXN3Q1FZRFZRUUcKRXdKT1dqRVNNQkFHQTFVRUNBd0pTR0YzYTJWelFtRjVNUll3RkFZRFZRUUhEQTFJWVhabGJHOWphMDV2Y25SbwpNUk13RVFZRFZRUUtEQXBHYVc1blpYSnRZWEpyTVJ3d0dnWURWUVFEREJObGVXVmpkV1ZrWVhOb1ltOWhjbVF1ClkyOXRNSUlCSWpBTkJna3Foa2lHOXcwQkFRRUZBQU9DQVE4QU1JSUJDZ0tDQVFFQXVXUEdqZWk5OVY2UEJSbXcKMVV3VHBuVmV5Y053bmRwbW9hcmh6QkhxMzVsUXVmeHN6R0lPbUhZRWczcDdjMGI1RGhKMy9tT3k2Wm1FaDRvdgpTUnF2K2VJSFM3M1pEaGVRcXRNWTd2emoxbWxHYTByVlRESFZrWGJOREx5aDYyeS9CMzYrSmpyN3FNLzRVZFd6CmtPMVdqbEgrSWpMUGxaVjh2VWtnK09TK3NzSmt5TUowczMxeGFsd2xxdmJOU1luOFpXNGY0dWhCb2RIOTE2V00KVC9QVVRwMGN1MHJCTDJXbkduemEyMTExMjNxVWRGVytsRzlUU3pHUVlGNUtONXZYUlA3ZlY1Y0pNWDIvbmVUVwplUHVUVklJUW1lbWtHaUdXalhvdlhCUmN3NmZmUTI0aFppQ3JrYnNpZ1pSTnRvclQyYXRKbVMyVE1maEdhNGxRCkw4YXQ3UUlEQVFBQm80R0hNSUdFTUlHQkJnTlZIUkVFZWpCNGdoTmxlV1ZqZFdWa1lYTm9ZbTloY21RdVkyOXQKZ2hkM2QzY3VaWGxsWTNWbFpHRnphR0p2WVhKa0xtTnZiWUlTZEdWemRDNXlZWFJoZEc5emEzSXVlSGw2Z2hGawpaWFl1Y21GMFlYUnZjMnR5TG5oNWVvSUpiRzlqWVd4b2IzTjBod1NzRkFBQmh3UUtqQytuaHdUQXFBS01od1RBCnFBTElNQTBHQ1NxR1NJYjNEUUVCQ3dVQUE0SUJBUUExWUVNc1ZCT21maWhYNENMUWw4V1k2ajBJSTMyWGZqV0sKM0ozVy9XVW1qS29EaFhVQ2xTcUFwTUV4NExLWFNyd1c3YUFMdTA4cjhWVDhGc2lCeFJxb3FCVEpWOTRzMTRyTQpzV0FiOEhxaGpFZlB3MlU1Y3VKbzZNMjFLU0tVb2JqcmlDZGJHM0E5L1B0OWdlLysrZWNMWXhRZnFLaDFmb21qCm41dy9TYlloSGFCaTJKalFIN29tTUtYWUgyQjdDaXVTTTZ4MlZBQWpZektzSTMzeWhxMnJyOUMvM0dubDhXa2sKbC8wTUxrdXJtM0toSU9qZ2o1b29oSWxFRXdGUDRodmFGS3hSNUtGcXdqRFd0TVUrVENjWG90TVdtSjJ2aGZvOAozNFdCeXZvVHZWa1pXNExIRldacXNMNXNZRURyeVBJemhRRUQzWGRicnIybHMwaFVmVmk2Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
    server_key: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  weights:
    weights_update_aws_access_key_id: ****************************
    weights_update_aws_default_region: YXAtc291dGhlYXN0LTI=
    weights_update_aws_secret_access_key: RWVlaHNUTnpsQ1VvSkdFYUJ4T1h6NXgyUi9nVis2dTRkUzdFQUZ5Nw==
  roi_suggestor_sqs:
    aws_access_key_id: ****************************
    aws_secret_access_key: S3RCTlJ2dUgwdytRQUorU25mYUt1N0dyTkdJTXlDajl0blc3TE5wUQ==
  camera_metrics_exporter:
    aws_access_key_id: ********************
    aws_secret_access_key: iLJUPATOaFx52zArCat3svyUDODhq92wzKXcKJJ1
    aws_default_region: ap-southeast-2
