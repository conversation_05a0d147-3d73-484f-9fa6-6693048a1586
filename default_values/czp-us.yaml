configuration:
  aws_region: us-west-1
  customer: czp-usa
  account_id: "************"
  images_bucket: eyecue-czp-us-images/new_images
  remote_metrics:
    url: https://victoria-metrics.us-east-1.fingermark.tech/insert/0/prometheus

containers:
  mosaic:
    AUTH_CLIENT_ID: rqFexQ6oGHypGosW1xZ3yyUlbJt4fvbo
    AUTH_CLIENT_SECRET: 7vx6jvZGSMITMFhQKTgK6oGvy9gqynoS3UO5ynjKk6zIluRprG3do3sc91zDBZPw
    dashboard_url: https://portal.eyecuedashboard.com/mosaic
    dashboard_version: "2"
    bucket: eyecue-czp-us-mosaic
  camera_displacement:
    images_bucket_captured: eyecue-czp-us-images
    images_bucket_config: eyecue-czp-us-camera-images
    aws_sqs_queue_name: eyecue-camera-displacement-sqs-czp-usa

credentials:
  eyecue_server:
    aws_access_key_id: ****************************
    aws_default_region: dXMtd2VzdC0x
    aws_secret_access_key: K1N1eHA3aUVLNmxyL0pYMnBZN2R5N3B3amlXVkw3QjdqZ3NuS2dZSg==
  image_sync_s3:
    aws_access_key_id: ****************************
    aws_default_region: dXMtd2VzdC0x
    aws_secret_access_key: KytvUUFqMjI4bkhoU0NjK3dhWmtnVHltZE4zeWVZQjhLRWNSVGZqVg==
  iot:
    aws_access_key_id: ****************************
    aws_default_region: dXMtd2VzdC0x
    aws_secret_access_key: d0hoanFva0N6amp2Q0RnODRnQjlUMTRiQ0RHRnhWNnV5anR6MVFqbg==
    endpoint: YTJ6Zm1rY3VraDFlemgtYXRzLmlvdC51cy13ZXN0LTEuYW1hem9uYXdzLmNvbQ==
  mosaic:
    aws_access_key_id: ****************************
    aws_default_region: dXMtd2VzdC0x
    aws_secret_access_key: Vi8yb0JzRkcwcUZFekN0Tno3UkVob3NNRjFrMk9uTHgxOHlXT0JNSg==
  self_signed_ssl_certificate:
    ca_cert: 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
    cert_sn: "1690856051"
    server_cert: 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
    server_key: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  weights:
    weights_update_aws_access_key_id: ****************************
    weights_update_aws_default_region: dXMtd2VzdC0x
    weights_update_aws_secret_access_key: K1JrT3pxK3l0ZTlOUWlueTcvcVFXY05sYnJqTkhFYy9sbHhiaFZLdw==
  roi_suggestor_sqs:
    aws_access_key_id: ****************************
    aws_secret_access_key: bk56ZTNZZVIwditQa1ZicE82My9GNDZ6cWNxSUd5bXFsTGNVN21sSQ==
  camera_metrics_exporter:
    aws_access_key_id: ********************
    aws_secret_access_key: cBYdElAomOj2jyXjt0XfpwkyeKghfUAmqHwMavo7
    aws_default_region: us-west-1
