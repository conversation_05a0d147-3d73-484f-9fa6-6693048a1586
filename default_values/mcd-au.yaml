configuration:
  aws_region: ap-southeast-2
  customer: mcd-aus
  account_id: "************"
  images_bucket: eyecue-mcdonalds-images/new_images

containers:
  nginx:
    enabled: true
  mosaic:
    bucket: eyecue-mcd-au-mosaic
  camera_displacement:
    images_bucket_captured: eyecue-mcdonalds-images
    images_bucket_config: eyecue-mcdonalds-aus-camera-images
    aws_sqs_queue_name: eyecue-camera-displacement-sqs-mcd-aus

credentials:
  eyecue_server:
    aws_access_key_id: ****************************
    aws_default_region: YXAtc291dGhlYXN0LTI=
    aws_secret_access_key: VTY4UnFSdzRhcCsxV0JHUWFablVoYzdKS2ZWaS9sU0duZFFsTU5RUA==
  image_sync_s3:
    aws_access_key_id: ****************************
    aws_default_region: YXAtc291dGhlYXN0LTI=
    aws_secret_access_key: SlBZQTl2YUpXNDh2cjNjTyt2dHFDS2NKMGQrcnExeVF2WThUeENXSg==
  iot:
    aws_access_key_id: ****************************
    aws_default_region: YXAtc291dGhlYXN0LTI=
    aws_secret_access_key: Q3VjdzRIUmk0YzhkVisrTStoUGNTemQxYWMwejhINnZVY3E4OFpKbg==
    endpoint: YTI1ZmhlNjlzZzJiNGItYXRzLmlvdC5hcC1zb3V0aGVhc3QtMi5hbWF6b25hd3MuY29t
  mosaic:
    aws_access_key_id: ****************************
    aws_default_region: YXAtc291dGhlYXN0LTI=
    aws_secret_access_key: WmNVajlTdTJwVEsweGdUMU42U1BFN3lUTXNaczFuRDVWVnc4WkJlMw==
  self_signed_ssl_certificate:
    ca_cert: 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
    cert_sn: "1646168391"
    server_cert: 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
    server_key: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  weights:
    weights_update_aws_access_key_id: ****************************
    weights_update_aws_default_region: YXAtc291dGhlYXN0LTI=
    weights_update_aws_secret_access_key: YitQYTNjTmxuTUV1V2IwTmNOK3FhRTlCK0g3cWpWbldMbFdnci9aQw==
  roi_suggestor_sqs:
    aws_access_key_id: ****************************
    aws_secret_access_key: TGxZNFpXM3NRMzFBNDNJY2UrS2M2Y0pCSFQxS3ZWTy9QUnY3UGNzaw==
  camera_metrics_exporter:
    aws_access_key_id: ********************
    aws_secret_access_key: 30u487jBaSwUdBBg2t65reElj6CKzIOG7weuZ2S0
    aws_default_region: ap-southeast-2
