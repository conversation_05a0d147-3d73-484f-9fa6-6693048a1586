configuration:
  aws_region: ap-southeast-2
  customer: mcd-nzl
  account_id: "************"
  images_bucket: eyecue-mnz-nzl-images/new_images

containers:
  nginx:
    enabled: true
  mosaic:
    bucket: eyecue-mnz-nzl-mosaic
  camera_displacement:
    images_bucket_captured: eyecue-mnz-nzl-images
    images_bucket_config: eyecue-mnz-nzl-camera-images
    aws_sqs_queue_name: eyecue-camera-displacement-sqs-mnz-nzl

credentials:
  eyecue_server:
    aws_access_key_id: ****************************
    aws_default_region: YXAtc291dGhlYXN0LTI=
    aws_secret_access_key: cWxyMXc3YVAxazM1ekZWRXNoTWx0a2NvWGdodE1ZNytqM25mK1ZHeg==
  image_sync_s3:
    aws_access_key_id: ****************************
    aws_default_region: YXAtc291dGhlYXN0LTI=
    aws_secret_access_key: UzBFZHhQaDNYbjNUVnRZb0JKMzZvT3J2UjFXUGFvamJKSEF5c1N1VQ==
  iot:
    aws_access_key_id: ****************************
    aws_default_region: YXAtc291dGhlYXN0LTI=
    aws_secret_access_key: SVBOV2RXUm5UTXN4WUdDd2RRaURTeERrbW11YkJxK2s1RVBycnBZUQ==
    endpoint: YTJldXNqdnhzajM3aDYtYXRzLmlvdC5hcC1zb3V0aGVhc3QtMi5hbWF6b25hd3MuY29t
  mosaic:
    aws_access_key_id: ****************************
    aws_default_region: YXAtc291dGhlYXN0LTI=
    aws_secret_access_key: Q1FCenRiVWVJd1dJLytsRy9MR1k1bCtZL0JyT2VIV1NTT0NROFNkcQ==
  self_signed_ssl_certificate:
    ca_cert: 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
    cert_sn: "1686183606"
    server_cert: 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
    server_key: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  weights:
    weights_update_aws_access_key_id: ****************************
    weights_update_aws_default_region: YXAtc291dGhlYXN0LTI=
    weights_update_aws_secret_access_key: M0FJS0tZNW56Umx2cDc0SVBCRlJ0Z0VUTk80cGlmZVFlR1kzYlh4Yg==
  roi_suggestor_sqs:
    aws_access_key_id: ****************************
    aws_secret_access_key: Sy9FTUhicnhYZ2YzY29jbCt1VVVMQzBpR1RrZXp0ZmFkcDRPT1FIbg==
  camera_metrics_exporter:
    aws_access_key_id: ********************
    aws_secret_access_key: XFfoL29whvTQUhg+apKB9OcNZPAlb7MDlQ2veBVx
    aws_default_region: ap-southeast-2
