---
configuration:
  cron_schedules:
    background_image_collection: 0 */6 * 10,11 1
  env: prod
  infra_deploy: true
  infra_namespace: infra
  ld_key: ****************************************
  context:
    rabbitmq_uri: amqp://{{ .Values.credentials.rabbitmq.username }}:{{ .Values.credentials.rabbitmq.password }}@eyecue-rabbitmq.{{ .Values.configuration.infra_namespace }}.svc.cluster.local:5672
    mqtt_uri: mqtt://{{ .Values.credentials.rabbitmq.username }}:{{ .Values.credentials.rabbitmq.password }}@eyecue-rabbitmq.{{ .Values.configuration.infra_namespace }}.svc.cluster.local:1883
    redis_uri: redis://redis.{{ .Values.configuration.infra_namespace }}.svc.cluster.local:6379?db=0
    mongodb_uri: mongodb://eyecue-mongodb.{{ .Values.configuration.infra_namespace }}.svc.cluster.local:27017
    metrics_endpoint: http://vmagent-svc.{{ .Values.configuration.infra_namespace }}.svc.cluster.local:8429
    minio_endpoint: http://eyecue-minio.{{ .Values.configuration.infra_namespace }}.svc.cluster.local:9000
    triton_endpoint: triton.{{ .Values.configuration.infra_namespace }}.svc.cluster.local:8001
  ports:
    grafana_port: 30003
    nginx_port: 80
    redis_port: 30379
  remote_metrics:
    enabled: true
    url: https://victoria-metrics.ap-southeast-2.fingermark.tech/insert/0/prometheus
    username: ingester
    password: YAXHrbCRg43iPpEJTNbw
  resources:
    triton:
      limits:
        memory: 2Gi
      requests:
        cpu: 750m
        memory: 2Gi
    eyecue_argus:
      limits:
        memory: 500Mi
      requests:
        cpu: 10m
        memory: 175Mi
    mosaic_recorder:
      limits:
        memory: 2Gi
      requests:
        cpu: 500m
        memory: 1Gi
    redis:
      requests:
        cpu: "1"
        memory: 500Mi
    eyeq_detector:
      limits:
        memory: 500Mi
      requests:
        cpu: 500m
        memory: 350Mi
    roi_suggestor:
      limits:
        cpu: 3000m
        memory: 8Gi
      requests:
        cpu: 250m
        memory: 512Mi
    roi_suggestor_assembler:
      requests:
        memory: 512Mi
        cpu: 250m
      limits:
        memory: 4Gi
        cpu: 3000m

containers:
  aggregated_data:
    enabled: true
    image:
      tag: 1.97.0
  background_image_collection:
    enabled: true
    active_deadline_seconds: 86400
    starting_deadline_seconds: 604800
    image:
      tag: 1.22.0
  best_shot_capturer:
    enabled: true
    image:
      tag: 1.96.0
  camera_config_changer:
    enabled: true
    image:
      tag: 1.80.1
  departures_mongodb:
    enabled: true
    image:
      tag: 1.80.2
  edge_metadata_gatekeeper:
    schedule: "0 * * * *"
    enabled: true
    image:
      tag: 1.46.0
  employee_counter:
    enabled: false
    image:
      tag: 1.69.0
  camera_displacement:
    enabled: true
    image:
      tag: 1.90.1
  eyecue_argus:
    enabled: true
    image:
      tag: 2.11.0
    sentry_io_connection_url: https://<EMAIL>/6328950
    sentry_release: 2.8.0
    resource_discovery_seconds: 86400
    environment: production
    nmap_ports_to_scan: 80,554
    nmap_timing: -T5
    nmap_timeout: "60" # in seconds
    network_interfaces: eno1,eno2,enp3s0,enp107s0
  eyeq_credentials:
    enabled: true
    image:
      tag: 3.0.0
  # Important services
  eyeq_detector:
    enabled: true
    image:
      tag: 5.11.2
    replica_set:
      number: 10
  eyeq_server:
    enabled: true
    image:
      tag: 4.30.3
  eyeq_tracker:
    enabled: true
    image:
      tag: 6.28.3
  # mosaic services
  mosaic:
    enabled: true
    recorder:
      image:
        tag: 6.14.0
    assembler:
      image:
        tag: 6.13.0

    AUTH_AUDIENCE: https://api.eyecuedataboard.com
    AUTH_CLIENT_ID: AeIxPzO8jIdLK5ldEAyd7NyG7guybpg8
    AUTH_CLIENT_SECRET: 6LED0AWZXnlNynsIDyaOiUMwBufLqLUuMhuKkn7u8IeVov6_QSVrDU-yIZHoPpKI
    AUTH_METHOD: client_token

    dashboard_password: nmp-fm-tst-nz-023
    dashboard_tab: "1"
    dashboard_url: https://test.ratatoskr.xyz/secure_login
    dashboard_username: nmp-fm-tst-nz-023
    dashboard_version: "1"

    slack_channel_id: C043BTBE1M3
    slack_token: *******************************************************
    slack_url: https://hooks.slack.com/triggers/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

    minio_bucket_name: eyecue-mosaic
    bucket: ""
  queue_zone:
    enabled: true
    image:
      tag: 1.89.1
  roi_suggestor:
    enabled: true
    assembler:
      image:
        tag: 1.96.0
    jobs:
      image:
        tag: 1.96.0
    slack_url: https://hooks.slack.com/triggers/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
    minio_bucket_name: eyecue-images
  training_data_capture:
    enabled: true
    image:
      tag: 1.76.0
  validation_tool:
    enabled: true
    ports:
      backend: 32333
      frontend: 32500
    image:
      tag: 0.9.1
  log_cleanup:
    enabled: true

  # Infra things enabled by configuration.infra_deploy
  vmalert:
    enabled: true
    image:
      tag: 1.6.0
  vmagent:
    enabled: true
    image:
      tag: 1.6.0
  alertmanager:
    enabled: true
    image:
      tag: 1.6.0
  victoriametrics:
    enabled: true
    image:
      tag: 1.6.0
  eyeq_grafana:
    image:
      tag: 1.6.0
  triton:
    args:
      - "--log-verbose=0"
      - "--grpc-infer-allocation-pool-size=16"
    image:
      tag: 24.08-py3 # this is the official NVIDIA tag
    models_bucket: eyecue-weights
    probes:
      enabled: true
  eyecue_weights_sync:
    image:
      tag: 0.0.12
  nginx:
    enabled: false
    image:
      tag: 2.0.0
  camera_metrics_exporter:
    enabled: true
    namespace: monitoring
    image:
      tag: 0.1.0
    env:
      - name: SITE_ID
        valueFrom:
          fieldRef:
            fieldPath: spec.nodeName
      - name: LOG_LEVEL
        value: INFO
      - name: UPDATE_INTERVAL
        value: "3600"
      - name: APP_PORT
        value: "8080"
      - name: APP_HOST
        value: "0.0.0.0"
  pre_puller:
    enabled: false
    image:
      tag: 3.6
    images:
      - nvcr.io/nvidia/tritonserver:24.08-py3

credentials:
  minio:
    username: minio-internal
    password: minio-internal-password
  docker:
    aws_access_key_id: ****************************
    aws_account_id: NjEzNjE1NDIyOTQx
    aws_default_region: YXAtc291dGhlYXN0LTI=
    aws_secret_access_key: UjBGKzZlNVRGb1c2VUg4eVd5ZFlTTXZ4ZS81RUhrUytGemd5UlhtZw==
  rabbitmq:
    password: 9VaAzQxtePupG6KetXWBJXtBiA
    username: services
  eyecue_weights_sync:
    aws_access_key_id: ********************
    aws_default_region: ap-southeast-2
    aws_secret_access_key: mkD5dVVBBcrRgrxfu+lQME1UqOfS14PEERE3jB68
  camera_health_check:
    aws_access_key_id: ********************=
    aws_secret_access_key: OwZnvbua40H7eqzPJhahQ3G2RcimQctUdYrAhYSE
    aws_default_region: ap-southeast-2

global:
  configuration:
    site_id: null
  cameras:
    - name: camera666
  docker_registry: ************.dkr.ecr.ap-southeast-2.amazonaws.com
  imagePullSecrets:
    - aws-registry-001
  log_level: INFO
  namespace: nmp-{{ .Values.global.hostname }}
  hostname: null

# https://github.com/bitnami/charts/blob/mongodb/16.4.12/bitnami/mongodb/values.yaml
mongodb: &mongodb
  enabled: true

  architecture: standalone
  useStatefulSet: true

  global:
    namespaceOverride: infra

  pdb:
    create: false

  metrics:
    enabled: true

  auth:
    enabled: false

  persistence:
    existingClaim: mongodb-pvc

  volumePermissions:
    enabled: true

  resources:
    requests:
      cpu: 300m
      memory: 1024Mi
    limits:
      memory: 1024Mi

  extraDeploy:
    - kind: PersistentVolumeClaim
      apiVersion: v1
      metadata:
        name: mongodb-pvc
        namespace: infra
      spec:
        accessModes:
          - "ReadWriteOnce"
        resources:
          requests:
            storage: 1Gi
        storageClassName: ""
        volumeName: mongodb-pv
    - kind: PersistentVolume
      apiVersion: v1
      metadata:
        name: mongodb-pv
      spec:
        capacity:
          storage: 1Gi
        accessModes:
          - ReadWriteOnce
        storageClassName: ""
        volumeMode: Filesystem
        persistentVolumeReclaimPolicy: Retain
        hostPath:
          path: /media/fingermark/storage/mongodb-infra

mongodb-backup:
  <<: *mongodb

  enabled: false

  nameOverride: mongodb-backup

  persistence:
    existingClaim: mongodb-backup-pvc

  extraDeploy:
    - kind: PersistentVolumeClaim
      apiVersion: v1
      metadata:
        name: mongodb-backup-pvc
        namespace: infra
      spec:
        accessModes:
          - "ReadWriteOnce"
        resources:
          requests:
            storage: 1Gi
        storageClassName: ""
        volumeName: mongodb-backup-pv
    - kind: PersistentVolume
      apiVersion: v1
      metadata:
        name: mongodb-backup-pv
      spec:
        capacity:
          storage: 1Gi
        accessModes:
          - ReadWriteOnce
        storageClassName: ""
        volumeMode: Filesystem
        persistentVolumeReclaimPolicy: Retain
        hostPath:
          path: /var/lib/mongodb-infra-backup

# https://github.com/bitnami/charts/blob/rabbitmq/15.0.3/bitnami/rabbitmq/values.yaml
rabbitmq:
  enabled: true

  namespaceOverride: infra

  pdb:
    create: false

  metrics:
    enabled: true

  extraPlugins: "rabbitmq_mqtt rabbitmq_web_mqtt"

  resources:
    requests:
      cpu: 300m
      memory: 300Mi
    limits:
      memory: 512Mi

  volumePermissions:
    enabled: true

  persistence:
    enabled: true
    existingClaim: rabbitmq-pvc

  auth:
    password: q1w2e3r4
    erlangCookie: eyecue

    tls:
      enabled: true
      existingSecret: eyeq-self-sign-ssl-credentials
      failIfNoPeerCert: false

  extraConfiguration: |
    web_mqtt.ssl.port       = 15676
    web_mqtt.ssl.cacertfile = /opt/bitnami/rabbitmq/certs/ca_certificate.pem
    web_mqtt.ssl.certfile   = /opt/bitnami/rabbitmq/certs/server_certificate.pem
    web_mqtt.ssl.keyfile    = /opt/bitnami/rabbitmq/certs/server_key.pem

  loadDefinition:
    enabled: true
    existingSecret: eyecue-rabbitmq-definition-secret

  clustering:
    enabled: false

  extraContainerPorts:
    - name: mqtt
      containerPort: 1883
    - name: mqtt-tls
      containerPort: 8883
    - name: web-mqtt
      containerPort: 15675
    - name: web-mqtt-tls
      containerPort: 15676

  networkPolicy:
    extraIngress:
      - ports:
          - port: 1883
            protocol: TCP
          - port: 8883
            protocol: TCP
          - port: 15675
            protocol: TCP
          - port: 15676
            protocol: TCP

  service:
    extraPorts:
      - name: mqtt
        port: 1883
        targetPort: mqtt
      - name: mqtt-tls
        port: 8883
        targetPort: mqtt-tls
      - name: web-mqtt
        port: 15675
        targetPort: web-mqtt
      - name: web-mqtt-tls
        port: 15676
        targetPort: web-mqtt-tls

    extraPortsHeadless:
      - name: mqtt
        port: 1883
        targetPort: 1883
      - name: mqtt-tls
        port: 8883
        targetPort: 8883
      - name: web-mqtt
        port: 15675
        targetPort: 15675
      - name: web-mqtt-tls
        port: 15676
        targetPort: 15676

  extraDeploy:
    - kind: PersistentVolumeClaim
      apiVersion: v1
      metadata:
        name: rabbitmq-pvc
        namespace: infra
      spec:
        accessModes:
          - ReadWriteOnce
        resources:
          requests:
            storage: 1Gi
        storageClassName: ""
        volumeName: rabbitmq-pv
    - kind: PersistentVolume
      apiVersion: v1
      metadata:
        name: rabbitmq-pv
      spec:
        capacity:
          storage: 1Gi
        accessModes:
          - ReadWriteOnce
        storageClassName: ""
        volumeMode: Filesystem
        persistentVolumeReclaimPolicy: Retain
        hostPath:
          path: /media/fingermark/storage/rabbitmq-infra
    - apiVersion: v1
      kind: Service
      metadata:
        name: eyecue-rabbitmq-external
        namespace: infra
        labels:
          app.kubernetes.io/service: eyecue-rabbitmq-external
      spec:
        type: NodePort
        ports:
          - port: 15672
            targetPort: 15672
            protocol: TCP
            name: management
            nodePort: 30672
          - port: 15675
            targetPort: 15675
            protocol: TCP
            name: web-mqtt
            nodePort: 30675
          - port: 15676
            targetPort: 15676
            protocol: TCP
            name: web-mqtt-tls
            nodePort: 30676
        selector:
          app.kubernetes.io/instance: eyecue
          app.kubernetes.io/name: rabbitmq
    - apiVersion: v1
      kind: Secret
      metadata:
        name: eyecue-rabbitmq-definition-secret
        namespace: infra
      stringData:
        load_definition.json: |
          {
            "users": [
              {
                "name": "{{ .Values.global.configuration.site_id }}",
                "password": "{{ .Values.global.configuration.site_id }}",
                "tags": "management"
              },
              {
                "name": "services",
                "password_hash": "83kurbIX0d4en9kQ1Q4X0WmiE1cWyOxA0SK++TV8z8MxT3dJ",
                "hashing_algorithm": "rabbit_password_hashing_sha256",
                "tags": "management"
              },
              {
                "name": "dashboard",
                "password_hash": "QO5hC9xpnfatjjIzQCF8Ii5gHzGwW2g1vdOR1qUIUHfVn6EF",
                "hashing_algorithm": "rabbit_password_hashing_sha256",
                "tags": "impersonator"
              }
            ],
            "vhosts": [
              {
                "name": "/"
              }
            ],
            "permissions": [
              {
                "user": "{{ .Values.global.configuration.site_id }}",
                "vhost": "/",
                "configure": ".*",
                "write": ".*",
                "read": ".*"
              },
              {
                "user": "services",
                "vhost": "/",
                "configure": ".*",
                "write": ".*",
                "read": ".*"
              },
              {
                "user": "dashboard",
                "vhost": "/",
                "configure": ".*",
                "write": ".*",
                "read": ".*"
              }
            ],
            "topic_permissions": [
              {
                "user": "{{ .Values.global.configuration.site_id }}",
                "vhost": "/",
                "exchange": "",
                "write": ".*",
                "read": ".*"
              },
              {
                "user": "services",
                "vhost": "/",
                "exchange": "",
                "write": ".*",
                "read": ".*"
              },
              {
                "user": "dashboard",
                "vhost": "/",
                "exchange": "",
                "write": ".*",
                "read": ".*"
              }
            ],
            "parameters": [],
            "policies": [
              {
                "vhost": "/",
                "name": "q-limit",
                "pattern": ".*",
                "apply-to": "queues",
                "definition": {
                  "max-length": 10000,
                  "message-ttl": 600000
                },
                "priority": 0
              }
            ],
            "queues": [],
            "exchanges": [],
            "bindings": []
          }
