configuration:
  aws_region: ap-southeast-2
  customer: pop-nzl
  account_id: "************"
  images_bucket: eyecue-pop-nz-images/

containers:
  triton:
    models_bucket: eyecue-weights
  mosaic:
    AUTH_CLIENT_ID: rqFexQ6oGHypGosW1xZ3yyUlbJt4fvbo
    AUTH_CLIENT_SECRET: 7vx6jvZGSMITMFhQKTgK6oGvy9gqynoS3UO5ynjKk6zIluRprG3do3sc91zDBZPw
    dashboard_url: https://portal.eyecuedashboard.com/mosaic
    bucket: eyecue-pop-nz-mosaic
  camera_displacement:
    images_bucket_captured: eyecue-pop-nz-images
    images_bucket_config: eyecue-pop-nz-camera-images
    aws_sqs_queue_name: eyecue-camera-displacement-sqs-pop-nzl

credentials:
  docker:
    aws_default_region: YXAtc291dGhlYXN0LTI=
  eyecue_server:
    aws_access_key_id: ****************************
    aws_default_region: YXAtc291dGhlYXN0LTI=
    aws_secret_access_key: dTVzY1pFUVlJRi9uRERURmlzd05jcEo3MlhuVm05MHRkV3R5L2RSVQ==
  image_sync_s3:
    aws_access_key_id: ****************************
    aws_default_region: YXAtc291dGhlYXN0LTI=
    aws_secret_access_key: a2poTUhhZ0lUN0EvcEJoZS9OdWZOeFpLK3ZITWdyLzM4UU1TdUREbw==
  iot:
    aws_access_key_id: ****************************
    aws_default_region: YXAtc291dGhlYXN0LTI=
    aws_secret_access_key: U3BZeFFVOXFyN1dpaVJHODE3bUtaTlZSTGt0VW04S3E5YWNHdE9JVA==
    endpoint: YTNlaXlvdmRxMWFiNGctYXRzLmlvdC5hcC1zb3V0aGVhc3QtMi5hbWF6b25hd3MuY29t
  mosaic:
    aws_access_key_id: ****************************
    aws_default_region: YXAtc291dGhlYXN0LTI=
    aws_secret_access_key: aytLR1RFcitVUFJWakZwZVl0SnZUdHRDQUhuNk5YMEJDaWdVSTU0Uw==
  self_signed_ssl_certificate:
    ca_cert: 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
    cert_sn: "1646168391"
    server_cert: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUQ0ekNDQXN1Z0F3SUJBZ0lKQUpvekRYOWhhc1luTUEwR0NTcUdTSWIzRFFFQkN3VUFNR3d4Q3pBSkJnTlYKQkFZVEFrNWFNUkl3RUFZRFZRUUlEQWxJWVhkclpYTkNZWGt4RmpBVUJnTlZCQWNNRFVoaGRtVnNiMk5yVG05eQpkR2d4RXpBUkJnTlZCQW9NQ2tacGJtZGxjbTFoY21zeEhEQWFCZ05WQkFNTUUyVjVaV04xWldSaGMyaGliMkZ5ClpDNWpiMjB3SGhjTk1qSXdNekF4TWpBMU9UVXhXaGNOTXpJd01qSTNNakExT1RVeFdqQnNNUXN3Q1FZRFZRUUcKRXdKT1dqRVNNQkFHQTFVRUNBd0pTR0YzYTJWelFtRjVNUll3RkFZRFZRUUhEQTFJWVhabGJHOWphMDV2Y25SbwpNUk13RVFZRFZRUUtEQXBHYVc1blpYSnRZWEpyTVJ3d0dnWURWUVFEREJObGVXVmpkV1ZrWVhOb1ltOWhjbVF1ClkyOXRNSUlCSWpBTkJna3Foa2lHOXcwQkFRRUZBQU9DQVE4QU1JSUJDZ0tDQVFFQXhWaGsyYVg1cUJqVW16cmsKSGkrQS9MM1NwVW44Ymp3dEV0QUNrdDV2L05tUGl4dlZDN0RNUXNVWFdob2w3c0dSaUNlK2JGQVhOR0UxS1M2cgp3MVU4UDg2ZDhzWVBnZnU1cmpYeEpSdUx4NWVVSU8xNFBmcEVDbkY0NWd5ODdKenY2T1Q4azVXeVJQYVZKMzJMCmVLYVRwOFZzS3dYZXBwcUh6aEQ0eDhjOUpobTE2MHNWNFpnUEVIa2FWWDBoM0NyZ2J1M1h6MWZaNGRTUDUzc0gKQmY0bWRIOGhoQzdvYlBFNVkyd2RzbHVEeVlQQWhNTzhpTkZNajlxQ0dsc2s4Vm0weHFGYklaV29oUDJwZVBVQgpBVGhtdjhmVWU4d1hPNy91SVJqL1hyWGJxWHNTc3RmL3hiZ085aER4MXl6b0E4NE5GVUU5SDk2azZaN1pGRENRCnhvWUFwd0lEQVFBQm80R0hNSUdFTUlHQkJnTlZIUkVFZWpCNGdoTmxlV1ZqZFdWa1lYTm9ZbTloY21RdVkyOXQKZ2hkM2QzY3VaWGxsWTNWbFpHRnphR0p2WVhKa0xtTnZiWUlTZEdWemRDNXlZWFJoZEc5emEzSXVlSGw2Z2hGawpaWFl1Y21GMFlYUnZjMnR5TG5oNWVvSUpiRzlqWVd4b2IzTjBod1FEQkFPYWh3VEFxQUVlaHdUQXFBRU5od1NzCkVBNERNQTBHQ1NxR1NJYjNEUUVCQ3dVQUE0SUJBUUIzMkdvcGdoOVBxdWQzOWdhblkyRDl0OXFjUkhUbytPY1EKUi9WdXFaL1VHcGkxNlExbXMwUDJIVnlMbC8wczJid1dIT3dRdHIrZk5RMlhrTjd2R29rU1NyNE5Bc0pua2IzSgorTVNIQVBLMmRCL3d2UGZ6QUtBeFhzbDdKbWdmcFEzU3RSREdoWnNrNHh2d3NJVnk2OEdpaHRFVmhsTG93MFMzCmdzc1prK2taLzdYWjNlVStlY0dSQ1NqNTVENFM5T0tNYjRIdlE4ZUlnRjMzR0o0NVBwR1JZNG11ck1HVVF1Vm8KWjBWZlNMY2JGUnhUWnNKRXdKVElzeHZVMFZmcEVZeW02bm9ZS1IwR3NLYm4vdDVFVm5OT0NYbnpKUTkvU2FPbQovMURWSHBGZkxUeXF5ZkhDREdIaGxMRUE3akhpT2VYNFhnV2xZd0FvMlNSNW9ybXYzYmNDCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
    server_key: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  weights:
    weights_update_aws_access_key_id: ****************************
    weights_update_aws_default_region: YXAtc291dGhlYXN0LTI=
    weights_update_aws_secret_access_key: Z3pNYXNmdGpsOHBSNFE1TkNobDJrZlpNNHp0YW4zUlZhVEF0cFdiNg==
  eyecue_weights_sync:
    aws_default_region: ap-southeast-2
  roi_suggestor_sqs:
    aws_access_key_id: ****************************
    aws_secret_access_key: NXZERVpHeUlBcTYvUWxwa1BrZlQ3MzNHcks2Rkg2WXBZUWxwWjEzYg==
  camera_metrics_exporter:
    aws_access_key_id: ********************
    aws_secret_access_key: bNYliWsZzMxxbijw/FfCQ3Tw/JqFk0RFY01hiW7f
    aws_default_region: ap-southeast-2

global:
  docker_registry: 613615422941.dkr.ecr.ap-southeast-2.amazonaws.com
