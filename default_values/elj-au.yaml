configuration:
  aws_region: ap-southeast-2
  customer: elj-aus
  account_id: "************"
  images_bucket: eyecue-elj-au-images/new_images

containers:
  nginx:
    enabled: true
  mosaic:
    bucket: eyecue-elj-au-mosaic
  camera_displacement:
    images_bucket_captured: eyecue-elj-au-images
    images_bucket_config: eyecue-elj-au-camera-images
    aws_sqs_queue_name: eyecue-camera-displacement-sqs-elj-aus

credentials:
  eyecue_server:
    aws_access_key_id: ****************************
    aws_default_region: YXAtc291dGhlYXN0LTI=
    aws_secret_access_key: S0FiVDlDdUFoWGFTWlZQd1FzSjJwZzZCbHBJZkdYZUVGZkxPOC9jZw==
  image_sync_s3:
    aws_access_key_id: ****************************
    aws_default_region: YXAtc291dGhlYXN0LTI=
    aws_secret_access_key: dVdsSmFNNXh3WUx0Z200Wnh3ZmI5cUNraklTbVpwZXNnU3ZITHFPbw==
  iot:
    aws_access_key_id: ****************************
    aws_default_region: YXAtc291dGhlYXN0LTI=
    aws_secret_access_key: Qi9oZ3grQjI2clRDUXJYSVZZNjVsVTduYmVtK2V0Q2liTWd1NGZLQQ==
    endpoint: YTI1ZmhlNjlzZzJiNGItYXRzLmlvdC5hcC1zb3V0aGVhc3QtMi5hbWF6b25hd3MuY29t
  mosaic:
    aws_access_key_id: ****************************
    aws_default_region: YXAtc291dGhlYXN0LTI=
    aws_secret_access_key: TnhkYkxUcjNvZTIvZ1piRFhaTXVmN1hVUXJmTyttdnpHbjdzSGFTWg==
  self_signed_ssl_certificate:
    ca_cert: 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
    cert_sn: "1690856018"
    server_cert: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUVTakNDQXpLZ0F3SUJBZ0lVQ3RRRlZIR1BiY3BSUVBJNHZPcHRsdnh2c0pBd0RRWUpLb1pJaHZjTkFRRUwKQlFBd2JERUxNQWtHQTFVRUJoTUNUbG94RWpBUUJnTlZCQWdNQ1VoaGQydGxjMEpoZVRFV01CUUdBMVVFQnd3TgpTR0YyWld4dlkydE9iM0owYURFVE1CRUdBMVVFQ2d3S1JtbHVaMlZ5YldGeWF6RWNNQm9HQTFVRUF3d1RaWGxsClkzVmxaR0Z6YUdKdllYSmtMbU52YlRBZUZ3MHlNekE0TURFd01qRXpNemhhRncwek16QTNNamt3TWpFek16aGEKTUd3eEN6QUpCZ05WQkFZVEFrNWFNUkl3RUFZRFZRUUlEQWxJWVhkclpYTkNZWGt4RmpBVUJnTlZCQWNNRFVoaApkbVZzYjJOclRtOXlkR2d4RXpBUkJnTlZCQW9NQ2tacGJtZGxjbTFoY21zeEhEQWFCZ05WQkFNTUUyVjVaV04xClpXUmhjMmhpYjJGeVpDNWpiMjB3Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLQW9JQkFRQ1oKVG1zRSs0SHoxWXdYWDdXR2pFellGVEhjREdrVGE1RzcyVGNUcDQ5TWpDb0N1TDQ2NmM3V3V6UTFHeElzakR3eApwMllPMEo1UEQzM0JPZno4dnFBVDFQRUprZjJuOFhnTDFoRGdKL3A3YTFFUVRCOE9XVHhEWm9SYXBNeCtmRnc1Ckt6SjV6UlNCK0FLdXc4ckJsSUp1dG0yeDUrVmdyL09tbi9zZlVHcWNIVytDTG5EdjZPbkdiZE43bnFYRWpVRXkKVzNBbkV1V2Y4Mi9pMEw1VDM3b29LY202QnUyZnhldVZXNjQ5R3dsNTcrbGtkNFE1U2xmSkRBOFVCWVZtaVRiMgpFUlNYblphQWpRNWFjclpDMjZqYXdMdklpaXhhdTBsN3VNTUp2VDYwenhJSyt5YmpZaWxoeXhCVHV2V3p5aEJ6Cno3dEp0OC9FMTJqcDdDTGFkc1BKQWdNQkFBR2pnZU13Z2VBd2diNEdBMVVkRVFTQnRqQ0JzNElUWlhsbFkzVmwKWkdGemFHSnZZWEprTG1OdmJZSVhkM2QzTG1WNVpXTjFaV1JoYzJoaWIyRnlaQzVqYjIyQ0VuUmxjM1F1Y21GMApZWFJ2YzJ0eUxuaDVlb0lSWkdWMkxuSmhkR0YwYjNOcmNpNTRlWHFDQ1d4dlkyRnNhRzl6ZElJWlozSmxaVzR1ClpYbGxZM1ZsWkdGemFHSnZZWEprTG1OdmJZSVlZbXgxWlM1bGVXVmpkV1ZrWVhOb1ltOWhjbVF1WTI5dGh3U3MKRkFBQmh3VEFxQllEaHdUQXFBRDdod1RBcUI2K2h3VEFxQ1NZTUIwR0ExVWREZ1FXQkJSUUNDRmJidVdob3lqYQpxZVVzT2JLWU9EV1lhREFOQmdrcWhraUc5dzBCQVFzRkFBT0NBUUVBRkR4REdPU0hDVHZ3eG8zUytwMTBKWHRjCk5icldGdzRuTGdDa2RSUXJMRkdLZFl5ZCs3SHB4ZmhtR05yOGN2MzdEakFkYkZOOWxlV2RTWGc4TjhzR3hlcVgKUWtZVUFtRExnS2JZNjRPWlM4Kzd3UWRWSjRibndQN3hnbHFSWndEeXVhMHBzR0hxU1RGSysyWFNIb2JXbWRwVApLUFJCK0tucWlRek4zTy9nS2trQ0RwZ3FDbEtMRkJuWjVYTk1SdUt0aGJzNldaa1VkaVBueE94RFRrZnBnSUVTCjE1aGd2L2l2SHA5d3UxZHh3dnVZV3ZRTG1neHhKUzcwMUVxRFNCd0h1VllhV2p1Tm5RQS85YUtzMm4rUEJUWU0KSXF5eXJkbDlma0w3Yng2SklCQ25mQUQ5bHljbDF4OTRZWmV4OW1GcHZSTTd0UnBXa1NTU21DaXBmQ0FtSnc9PQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
    server_key: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  weights:
    weights_update_aws_access_key_id: ****************************
    weights_update_aws_default_region: YXAtc291dGhlYXN0LTI=
    weights_update_aws_secret_access_key: ZEUrZXlPNFJ4TTFObzhHbDd3Y0FxKzNQa3ZYNkpNQnNLbXZrbTlBYw==
  roi_suggestor_sqs:
    aws_access_key_id: ****************************
    aws_secret_access_key: NEhzVDdhRUVRdmQrVG1hTmpWTm5OT1BrSndTenpMZTJlU0hVcTRBUg==
  camera_metrics_exporter:
    aws_access_key_id: ********************
    aws_secret_access_key: Y0LJqW14Vm8iJOVNQUOBTAnn2PpGg4UwZbkKv03Z
    aws_default_region: ap-southeast-2
