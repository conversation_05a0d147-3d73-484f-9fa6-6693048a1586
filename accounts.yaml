---
buckets:
  au-bucket:
    name: eyecue-helm-cv-prod-package
    region: ap-southeast-2
  us-bucket:
    name: eyecue-helm-cv-prod-us-package
    region: us-east-1
default: default_values/default.yaml
sites:
  dev-nzl:
    account: '************'
    file: default_values/dev-nzl.yaml
    name: dev-nzl
    region: ap-southeast-2
    test-site: fm-dev-nzl-00001
    client_id: dev-nzl
  stg-nzl:
    account: '************'
    file: default_values/stg-nzl.yaml
    name: stg-nzl
    region: ap-southeast-2
    test-site: fm-stg-nzl-00001
    client_id: stg-nzl
  bkg-nz:
    account: '************'
    file: default_values/bkg-nz.yaml
    name: bkg-nz
    region: ap-southeast-2
    test-site: fm-bkg-nzl-00048
    client_id: bkg
  cfa-us:
    account: '************'
    bucket-name: us-bucket
    file: default_values/cfa-us.yaml
    name: cfa-us
    region: us-east-1
    test-site: fm-cfa-usa-023
    client_id: cfa
  cul-usa:
    account: '************'
    file: default_values/cul-usa.yaml
    name: cul-usa
    region: us-east-1
    test-site: fm-cul-usa-00240
  czp-us:
    account: '************'
    docker-region: ap-southeast-2
    file: default_values/czp-us.yaml
    name: czp-us
    region: us-west-1
    test-site: fm-czp-us-023
    client_id: czp
  elj-au:
    account: '************'
    file: default_values/elj-au.yaml
    name: elj-au
    region: ap-southeast-2
    test-site: fm-elj-aus-023
    client_id: elj
  kfc-au:
    account: '************'
    file: default_values/kfc-au.yaml
    name: kfc-au
    region: ap-southeast-2
    test-site: fm-kfc-au-023
    client_id: kfc
  mcd-au:
    account: '************'
    file: default_values/mcd-au.yaml
    name: mcd-au
    region: ap-southeast-2
    test-site: fm-mcd-aus-023
    client_id: mcd
  mcd-nz:
    account: '************'
    file: default_values/mcd-nz.yaml
    name: mcd-nz
    region: ap-southeast-2
    test-site: fm-mcd-nz-023
    client_id: mnz
  poc:
    account: '************'
    file: default_values/poc-au.yaml
    name: poc
    region: ap-southeast-2
    test-site: fm-poc-gtm-023
  pop-nzl:
    account: '************'
    file: default_values/pop-nzl.yaml
    name: pop-nzl
    region: ap-southeast-2
    test-site: fm-pop-nzl-03001
    client_id: pop
  qa-au:
    account: '************'
    file: default_values/qa-au.yaml
    name: qa-au
    region: ap-southeast-2
    test-site: fm-tst-nz-023
    client_id: qa
  stb-nzl:
    account: '************'
    file: default_values/stb-nzl.yaml
    name: stb-nzl
    region: ap-southeast-2
    test-site: fm-stb-nzl-00048
  tim-can:
    account: '************'
    file: default_values/tim-can.yaml
    name: tim-can
    region: ca-central-1
    test-site: fm-tim-can-00048
  zmb-aus:
    account: '************'
    file: default_values/zmb-aus.yaml
    name: zmb-aus
    region: ap-southeast-2
    test-site: fm-zmb-aus-00048
  mcd-can:
    account: "************"
    file: default_values/mcd-can.yaml
    name: mcd-can
    region: ca-central-1
    test-site: fm-mcd-can-04463
  bkg-usa:
    account: "************"
    file: default_values/bkg-usa.yaml
    name: bkg-usa
    region: us-east-1
    test-site: fm-bkg-usa-00001
    client_id: bkg
  pop-usa:
    account: "************"
    file: default_values/pop-usa.yaml
    name: pop-usa
    region: us-east-1
    test-site: fm-pop-usa-00001
