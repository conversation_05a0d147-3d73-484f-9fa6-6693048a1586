## 11.147.0 (2025-08-22)

## 11.146.1 (2025-08-13)

### Feat

- update to 15 seconds
- add sleep prior to update version EYECUE-000

### Fix

- APP_VERSION EYECUE-2741
- whitespace EYECUE-000

### Refactor

- remove generate-staging-templates from pipeline

## 11.146.0 (2025-08-11)

## 11.145.1 (2025-08-05)

### Feat

- Send Pull Forward Count and Percentage via /eyeq/rolling/ for rest of fleet EYECUE-2631

### Fix

- revert eyecue_weights_sync tag from 0.0.14 to 0.0.12
- base integration EYECUE-2610

### Refactor

- remove unused alpr and Tessera configurations

## 11.145.0 (2025-07-29)

### Feat

- Send Pull Forward Count and Percentage via /eyeq/rolling/ for Popeyes US EYECUE-2631
- Send Pull Forward Count and Percentage via /eyeq/rolling/ for Popeyes US EYECUE-2631
- add pipeline to update image tag
- remove deprecated scripts and add update version script

### Fix

- check lambda response in update_version.py
- update pull request title and source branch name to development branch
- improve error handling in update-image-tag script
- tracker version EYECUE-2512
- tracker and server user EYECUE-2512
- paths permissions EYECUE-2512
- tracker permissions EYECUE-2512
- test tracker EYECUE-2512

## 11.144.0 (2025-07-27)

### Feat

- Implement new configurations to setup Heatmaps for Indoor roi suggestor EYECUE-2428
- updating eyecue-weights-sync and best-shot-capturer
- removing best shot saver EYECUE-2236
- adding option to capture best-shot only from detector's bboxes

### Fix

- remove stb-us
- remove portillos
- implement browser restarts via feature flag config EYECUE-2490
- updating wrong image values EYECUE-0000
- new common EYECUE-2413
- changing ambiguous name of env variable EYECUE-2236
- site id env variable EYECUE-2236
- **camera_displacement**: update version EYECUE-2179

## 11.143.0 (2025-07-21)

### Feat

- auto open pr every 2 weeks EYECUE-0000

## 11.142.0 (2025-07-21)

### Fix

- merge issues EYECUE-0000

## 11.141.0 (2025-07-21)

### Feat

- auto open pr every 2 weeks EYECUE-0000
- auto open pr every 2 weeks EYECUE-0000

### Refactor

- remove minio configuration

## 11.140.0 (2025-07-07)

### Feat

- updating mosaic recorder EYECUE-2487
- new common and python EYECUE-2413
- new common p13.3 eyecue-ops

### Fix

- bug in queue zone module EYECUE-2487

## 11.139.0 (2025-07-01)

### Feat

- add nmap_timeout configuration to eyecue_argus container

### Fix

- add comment to nmap_timeout

## 11.138.0 (2025-07-01)

### Feat

- add enabled value for victoria metrics

### Fix

- vmagent typo

## 11.137.0 (2025-06-26)

### Feat

- add self-signed SSL certificates for dev-nzl and stg-nzl
- add values for dev-nzl and stg-nzl

### Fix

- update endpoint formatting in dev-nzl and stg-nzl configuration files
- update AWS access keys in dev-nzl and stg-nzl

## 11.136.0 (2025-06-23)

### Feat

- new temp Victoria Metrics counters for aggregated journeys in CFA EYECUE-2271

## 11.135.0 (2025-06-16)

## 11.134.0 (2025-06-10)

### Feat

- add option to disable liveness probe configuration for triton
- remove node exporter and kube state metrics

## 11.133.3 (2025-06-09)

### Fix

- Only perform trajectory smoothing logic for indoor EYECUE-2158 Spiking TET times in several stores Ready to release EYECUE-2206

## 11.133.2 (2025-06-09)

### Fix

- re-enabling automatica deployments for Blaxland

## 11.133.1 (2025-06-08)

## 11.133.0 (2025-06-08)

### Feat

- add bitnami minio

## 11.129.1 (2025-05-21)

### Fix

- update argocd job targets in vmagent-configmap.yaml

## 11.132.0 (2025-06-03)

### Feat

- adding velocity data to VM EYECUE-2240

## 11.130.0 (2025-05-25)

### Feat

- Move "heatmap" logic to ROI suggestor assembler, "trajectory clustering" and "heatmap" ROI Suggestion images for Indoor EYECUE-1989 EYECUE-2061 EYECUE-2126

## 11.129.2 (2025-05-25)

## 11.131.0 (2025-06-03)
