apiVersion: v2
appVersion: 0.5.0
description: A Helm chart for EYECue
name: eyecue
type: application
version: 0.5.0
dependencies:
  - name: rabbitmq
    repository: oci://registry-1.docker.io/bitnamicharts
    version: 15.x.x
    condition: rabbitmq.enabled
  - name: mongodb
    repository: oci://registry-1.docker.io/bitnamicharts
    version: 16.x.x
    condition: mongodb.enabled
  - name: mongodb
    alias: mongodb-backup
    repository: oci://registry-1.docker.io/bitnamicharts
    version: 16.x.x
    condition: mongodb-backup.enabled
