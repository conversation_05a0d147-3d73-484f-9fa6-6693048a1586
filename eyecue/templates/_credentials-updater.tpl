{{- define "eyecue.credentials-updater.spec" -}}
  {{- $rootContext := .rootContext -}}
serviceAccountName: ecr-cred-updater
terminationGracePeriodSeconds: 0
restartPolicy: OnFailure
containers:
  - name: kubectl
    image: docker.io/heyvaldemar/aws-kubectl:6a87312f722582ec099f9fa276000dc8ca73590e
    command:
      - "/bin/sh"
      - "-c"
      - |
        set -e
        echo "Starting ECR credentials update..."

        # Define Docker registry server
        DOCKER_REGISTRY_SERVER=https://${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_DEFAULT_REGION}.amazonaws.com
        DOCKER_USER=AWS

        # Get Docker password
        DOCKER_PASSWORD=$(aws ecr get-login-password --region ${AWS_DEFAULT_REGION})
        if [ -z "$DOCKER_PASSWORD" ]; then
          echo "Failed to retrieve Docker password from AWS ECR."
          exit 1
        fi

        # Delete existing secret (if any)
        echo "Deleting existing secret (if it exists)..."
        kubectl delete secret aws-registry-001 --ignore-not-found

        # Create new Docker registry secret
        echo "Creating new Docker registry secret..."
        kubectl create secret docker-registry aws-registry-001 \
          --docker-server=$DOCKER_REGISTRY_SERVER \
          --docker-username=$DOCKER_USER \
          --docker-password=$DOCKER_PASSWORD \
          --docker-email=<EMAIL>

        # Patch the default service account to use the new secret
        echo "Patching default service account with imagePullSecrets..."
        kubectl patch serviceaccount default -p '{"imagePullSecrets":[{"name":"aws-registry-001"}]}'

        echo "ECR credentials update completed successfully."
    envFrom:
      - secretRef:
          name: aws-docker-credentials
nodeSelector:
  kubernetes.io/hostname: {{ $rootContext.Values.global.hostname }}
{{- end -}}

{{- define "eyecue.credentials-updater.secret" -}}
  {{- $credentials := .rootContext.Values.credentials -}}
---
apiVersion: v1
kind: Secret
metadata:
  name: aws-docker-credentials
  annotations:
    "helm.sh/hook-delete-policy": before-hook-creation
  namespace: {{ .namespace }}
data:
  AWS_ACCESS_KEY_ID: {{ $credentials.docker.aws_access_key_id }}
  AWS_SECRET_ACCESS_KEY: {{ $credentials.docker.aws_secret_access_key }}
  AWS_DEFAULT_REGION: {{ $credentials.docker.aws_default_region }}
  AWS_ACCOUNT_ID: {{ $credentials.docker.aws_account_id }}
{{- end -}}

{{- define "eyecue.credentials-updater.service-account" -}}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: ecr-cred-updater
  namespace: {{ .namespace }}
{{- end -}}

{{- define "eyecue.credentials-updater.cluster-role" -}}
---
kind: Role
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: ecr-cred-updater
  namespace: {{ .namespace }}
rules:
  - apiGroups: [""]
    resources: ["secrets"]
    verbs: ["get", "create", "delete"]
  - apiGroups: [""]
    resources: ["serviceaccounts"]
    verbs: ["get", "patch"]

---
kind: RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: ecr-cred-updater
  namespace: {{ .namespace }}
subjects:
  - kind: ServiceAccount
    name: ecr-cred-updater
roleRef:
  kind: Role
  name: ecr-cred-updater
  apiGroup: rbac.authorization.k8s.io
{{- end -}}

{{- define "eyecue.credentials-updater.job" -}}
---
apiVersion: batch/v1
kind: Job
metadata:
  # Jobs are immutable, so we are now v2!
  name: ecr-cred-updater-v2
  namespace: {{ .namespace }}
spec:
  backoffLimit: 4
  template:
    spec: {{ include "eyecue.credentials-updater.spec" . | nindent 6 }}
{{- end -}}

{{- define "eyecue.credentials-updater.cronjob" -}}
---
apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: ecr-cred-updater
  namespace: {{ .namespace }}
spec:
  schedule: "0 */8 * * *"
  concurrencyPolicy: Replace
  startingDeadlineSeconds: 300
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1
  jobTemplate:
    spec:
      backoffLimit: 4
      template:
        spec: {{ include "eyecue.credentials-updater.spec" . | nindent 10 }}
{{- end -}}

{{- define "eyecue.credentials-updater" -}}
{{ include "eyecue.credentials-updater.service-account" . }}
{{ include "eyecue.credentials-updater.cluster-role" . }}
{{ include "eyecue.credentials-updater.secret" . }}
{{ include "eyecue.credentials-updater.job" . }}
{{ include "eyecue.credentials-updater.cronjob" . }}
{{- end -}}
