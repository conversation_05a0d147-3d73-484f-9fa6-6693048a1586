{{- if $.Values.containers.eyeq_detector.enabled }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: "eyeq-detector"
  labels:
    app: "eyeq-detector"
  namespace: {{ include "eyecue.namespace" . }}
spec:
  selector:
    matchLabels:
      app: "eyeq-detector"
  replicas: {{ $.Values.containers.eyeq_detector.replica_set.number }}
  template:
    metadata:
      labels:
        app: "eyeq-detector"
      annotations:
        checksum/config: {{ include "eyecue.services-context-hash"  (dict "rootContext" .) }}
        checksum/secret: {{ include "eyecue.services-secret-hash"  (dict "rootContext" .) }}
    spec:
      initContainers:
        - name: check-tritonserver
          image: nicolaka/netshoot:v0.9
          env:
            - name: TRITON_INFERENCE_ENDPOINT
              valueFrom:
                secretKeyRef:
                  name: services-secret
                  key: TRITON_INFERENCE_ENDPOINT
          command: [ 'sh', '-c', "until nc -zv $(echo $TRITON_INFERENCE_ENDPOINT | sed 's/:.*//') 8001; do echo waiting for Triton Inference; sleep 2; done" ]
      containers:
        - name: "eyeq-detector"
          image: "{{ $.Values.global.docker_registry }}/eyeq-detector:{{ $.Values.containers.eyeq_detector.image.tag }}"

          livenessProbe:
            httpGet:
              path: /health
              port: 8000
            initialDelaySeconds: 3
            periodSeconds: 5

          {{- with $.Values.configuration.resources.eyeq_detector }}
          resources:
            {{- toYaml . | nindent 12 }}
          {{- end }}

          envFrom:
            - configMapRef:
                name: service-context
            - secretRef:
                name: services-secret
          env:
            - name: LOGURU_LEVEL
              value: {{ $.Values.containers.eyeq_detector.log_level | default $.Values.global.log_level }}
            - name: site_id
              value: "{{ $.Values.global.configuration.site_id }}"

            - name: IOT_ENDPOINT
              valueFrom:
                secretKeyRef:
                  name: aws-iot-credentials
                  key: IOT_ENDPOINT
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: aws-weights-update-credentials
                  key: WEIGHTS_UPDATE_AWS_ACCESS_KEY_ID

            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: aws-weights-update-credentials
                  key: WEIGHTS_UPDATE_AWS_SECRET_ACCESS_KEY

            - name: AWS_DEFAULT_REGION
              valueFrom:
                secretKeyRef:
                  name: aws-weights-update-credentials
                  key: WEIGHTS_UPDATE_AWS_DEFAULT_REGION
          volumeMounts:
            - name: localtime
              mountPath: /etc/localtime
              readOnly: true

            - name: timezone
              mountPath: /etc/timezone
              readOnly: true

            - name: runfiles
              mountPath: /root/run_files

            - name: logs
              mountPath: /root/logs

            - name: credentials
              mountPath: /root/keys/
              readOnly: true

      volumes:
        - name: credentials
          secret:
            secretName: "eyeq-utils-{{ $.Values.global.configuration.site_id }}"
        - name: localtime
          hostPath:
            path: /etc/localtime

        - name: timezone
          hostPath:
            path: /etc/timezone

        - name: runfiles
          hostPath:
            path: /opt/eyeq/run_files

        - name: logs
          hostPath:
            path: /opt/eyeq/logs

      imagePullSecrets:
        {{- range .Values.global.imagePullSecrets }}
        - name: {{ . }}
        {{- end }}
      nodeSelector:
        kubernetes.io/hostname: {{ $.Values.global.hostname }}
{{- end}}
