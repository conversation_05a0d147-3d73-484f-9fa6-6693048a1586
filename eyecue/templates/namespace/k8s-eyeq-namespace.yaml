{{- $namespace := include "eyecue.namespace" . }}
---
apiVersion: v1
kind: Namespace
metadata:
  name: {{ $namespace }}

{{ include "eyecue.credentials-updater" (dict "rootContext" . "namespace" $namespace) }}
{{ include "eyecue.services-secret" (dict "rootContext" . "namespace" $namespace) }}
{{ include "eyecue.services-context" (dict "rootContext" . "namespace" $namespace) }}
{{ include "eyecue.minio-secret" (dict "rootContext" . "namespace" $namespace) }}
