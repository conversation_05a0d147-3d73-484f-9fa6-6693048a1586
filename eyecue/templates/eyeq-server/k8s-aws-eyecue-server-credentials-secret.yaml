{{- $namespace := include "eyecue.namespace" . }}
---
apiVersion: v1
kind: Secret
metadata:
  name: aws-eyecue-server-credentials
  namespace: {{ $namespace }}
data:
  EYECUE_SERVER_AWS_ACCESS_KEY_ID: {{ .Values.credentials.eyecue_server.aws_access_key_id }}
  EYECUE_SERVER_AWS_SECRET_ACCESS_KEY: {{ .Values.credentials.eyecue_server.aws_secret_access_key }}
  EYECUE_SERVER_AWS_DEFAULT_REGION: {{ .Values.credentials.eyecue_server.aws_default_region }}
