{{- if $.Values.containers.eyeq_server.enabled }}
{{- $namespace := include "eyecue.namespace" . }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: eyeq-server
  labels:
    app: "eyeq-server"
  namespace: {{ $namespace }}
spec:
  selector:
    matchLabels:
      app: "eyeq-server"
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: "eyeq-server"
      annotations:
        checksum/config: {{ include "eyecue.services-context-hash"  (dict "rootContext" .) }}
        checksum/secret: {{ include "eyecue.services-secret-hash"  (dict "rootContext" .) }}
    spec:
      securityContext:
        fsGroup: 1000
      initContainers:
        - name: check-tritonserver
          image: nicolaka/netshoot:v0.9
          env:
            - name: TRITON_INFERENCE_ENDPOINT
              valueFrom:
                secretKeyRef:
                  name: services-secret
                  key: TRITON_INFERENCE_ENDPOINT
          command: [ 'sh', '-c', "until nc -zv $(echo $TRITON_INFERENCE_ENDPOINT | sed 's/:.*//') 8001; do echo waiting for Triton Inference Server; sleep 2; done" ]

        - name: fix-permissions
          image: busybox
          command: ["sh", "-c", "chown -R 1000:1000 /opt/nginx && chmod -R 775 /opt/nginx && chown -R 1000:1000 /opt/confs && chmod -R 775 /opt/confs && chown -R 1000:1000 /opt/detector_files && chmod -R 775 /opt/detector_files && chown -R 1000:1000 /opt/store_files && chmod -R 775 /opt/store_files && chown -R 1000:1000 /opt/logs && chmod -R 775 /opt/logs"]
          volumeMounts:
            - name: nginx
              mountPath: /opt/nginx
            - name: logs
              mountPath: /opt/logs
            - name: reacquirement
              mountPath: /opt/store_files
            - name: detector
              mountPath: /opt/detector_files
            - name: runfiles
              mountPath: /opt/confs
          securityContext:
            runAsUser: 0
            runAsGroup: 0

      containers:
        - name: eyeq-server
          image: "{{ $.Values.global.docker_registry }}/eyeq-server:{{ $.Values.containers.eyeq_server.image.tag }}"
          securityContext:
            runAsUser: 1000
            runAsGroup: 1000
          envFrom:
            - configMapRef:
                name: service-context
            - secretRef:
                name: services-secret
          env:
            - name: DISTANCE_METRIC
              value: "cosine"
            - name: LOGURU_LEVEL
              value: {{ $.Values.containers.eyeq_server.log_level | default $.Values.global.log_level }}
            - name: THING_NAME
              value: "eyeq-server-{{ .Values.global.configuration.site_id }}"
            - name: IOT_ENDPOINT
              valueFrom:
                secretKeyRef:
                  name: aws-iot-credentials
                  key: IOT_ENDPOINT
            - name: create_api
              value: http://eyecue-cloud-sync.{{ $namespace }}.svc.cluster.local:10005
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: aws-eyecue-server-credentials
                  key: EYECUE_SERVER_AWS_ACCESS_KEY_ID

            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: aws-eyecue-server-credentials
                  key: EYECUE_SERVER_AWS_SECRET_ACCESS_KEY

            - name: AWS_DEFAULT_REGION
              valueFrom:
                secretKeyRef:
                  name: aws-eyecue-server-credentials
                  key: EYECUE_SERVER_AWS_DEFAULT_REGION

            - name: NODE_IP
            {{- if  $.Values.containers.eyeq_server.ip }}
              value: "{{ $.Values.containers.eyeq_server.ip }}"
            {{- else}}
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
            {{- end}}

          volumeMounts:
            - name: localtime
              mountPath: /etc/localtime
              readOnly: true

            - name: timezone
              mountPath: /etc/timezone
              readOnly: true

            - name: runfiles
              mountPath: /opt/confs

            - name: detector
              mountPath: /opt/detector_files

            - name: reacquirement
              mountPath: /opt/store_files

            - name: nginx
              mountPath: /opt/nginx

            - name: logs
              mountPath: /opt/logs

            - name: credentials
              mountPath: /opt/keys/
              readOnly: true

      volumes:
        - name: credentials
          secret:
            secretName: eyeq-server-{{ .Values.global.configuration.site_id }}

        - name: localtime
          hostPath:
            path: /etc/localtime

        - name: timezone
          hostPath:
            path: /etc/timezone

        - name: runfiles
          hostPath:
            path: /opt/eyeq/run_files

        - name: detector
          hostPath:
            path: /opt/eyeq/detector_files
        - name: logs
          hostPath:
            path: /opt/eyeq/logs

        - name: nginx
          hostPath:
            path: /media/fingermark/storage/nginx

        - name: reacquirement
          hostPath:
            path: /media/fingermark/storage/reacquirement/

      imagePullSecrets:
        {{- range .Values.global.imagePullSecrets }}
        - name: {{ . }}
        {{- end }}

      nodeSelector:
        kubernetes.io/hostname: {{ $.Values.global.hostname }}
{{- end}}
