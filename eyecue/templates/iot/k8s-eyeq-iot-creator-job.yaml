{{- if $.Values.containers.eyeq_credentials.enabled }}
{{- $namespace := include "eyecue.namespace" . }}
---
apiVersion: batch/v1
kind: Job
metadata:
  name: eyeq-iot-creator
  namespace: {{ include "eyecue.namespace" . }}

spec:
  backoffLimit: 4
  template:
    spec:
      serviceAccountName: eyeq-iot-creator
      terminationGracePeriodSeconds: 0
      restartPolicy: OnFailure
      containers:
        - name: eyeq-cred-updater
          image: "{{ $.Values.global.docker_registry }}/eyeq-credentials:{{ $.Values.containers.eyeq_credentials.image.tag }}"
          command: ["python3"]
          args: ["main.py"]

          env:
            - name: HOSTNAME
              value: "{{ .Values.global.configuration.site_id }}"

            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: aws-iot-credentials
                  key: IOT_AWS_ACCESS_KEY_ID

            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: aws-iot-credentials
                  key: IOT_AWS_SECRET_ACCESS_KEY

            - name: AWS_DEFAULT_REGION
              valueFrom:
                secretKeyRef:
                  name: aws-iot-credentials
                  key: IOT_AWS_DEFAULT_REGION
      imagePullSecrets:
        {{- range .Values.global.imagePullSecrets }}
        - name: {{ . }}
        {{- end }}

      nodeSelector:
        kubernetes.io/hostname: {{ $.Values.global.hostname }}
{{- end}}
