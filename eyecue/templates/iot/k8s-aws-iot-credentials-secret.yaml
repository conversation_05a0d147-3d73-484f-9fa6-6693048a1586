{{- $namespace := include "eyecue.namespace" . }}
---
apiVersion: v1
kind: Secret
metadata:
  name: aws-iot-credentials
  namespace: {{ $namespace }}
data:
  IOT_AWS_ACCESS_KEY_ID: {{ .Values.credentials.iot.aws_access_key_id }}
  IOT_AWS_SECRET_ACCESS_KEY: {{ .Values.credentials.iot.aws_secret_access_key }}
  IOT_AWS_DEFAULT_REGION: {{ .Values.credentials.iot.aws_default_region }}
  IOT_ENDPOINT: {{ .Values.credentials.iot.endpoint }}
