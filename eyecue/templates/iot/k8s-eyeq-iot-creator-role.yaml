{{- $namespace := include "eyecue.namespace" . }}
---
kind: Role
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: eyeq-iot-creator
  namespace: {{ $namespace }}
rules:
  - apiGroups: [""]
    resources: ["secrets"]
    verbs: ["get", "create", "delete", "patch"]
  - apiGroups: [""]
    resources: ["serviceaccounts"]
    verbs: ["get", "patch"]

---
kind: RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: eyeq-iot-creator
  namespace: {{ $namespace }}
subjects:
  - kind: ServiceAccount
    name: eyeq-iot-creator
roleRef:
  kind: Role
  name: eyeq-iot-creator
  apiGroup: rbac.authorization.k8s.io
