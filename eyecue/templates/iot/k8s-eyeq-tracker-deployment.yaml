{{- if $.Values.containers.eyeq_tracker.enabled }}
{{- range $cameras := $.Values.configuration.cameras | default $.Values.global.cameras }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: "eyeq-tracker-{{ $.Values.global.configuration.site_id }}-{{ .name }}"
  labels:
    app: "eyeq-tracker"
    camera: "{{ .name }}"
  namespace: {{ include "eyecue.namespace" $ }}
spec:
  selector:
    matchLabels:
      name: "eyeq-tracker-{{ $.Values.global.configuration.site_id }}-{{ .name }}"

  template:
    metadata:
      labels:
        name: "eyeq-tracker-{{ $.Values.global.configuration.site_id }}-{{ .name }}"
        app: "eyeq-tracker"
        camera: "{{ .name }}"
      annotations:
        checksum/config: {{ include "eyecue.services-context-hash"  (dict "rootContext" $) }}
        checksum/secret: {{ include "eyecue.services-secret-hash"  (dict "rootContext" $) }}
    spec:
      securityContext:
        fsGroup: 1000
      initContainers:

      - name: fix-permissions
        image: busybox
        command: ["sh", "-c", "chown -R 1000:1000 /opt/run_files && chmod -R 775 /opt/run_files && chown -R 1000:1000 /opt/logs && chmod -R 775 /opt/logs"]
        volumeMounts:
          - name: logs
            mountPath: /opt/logs
          - name: runfiles
            mountPath: /opt/run_files
        securityContext:
          runAsUser: 0
          runAsGroup: 0

      - name: check-victoria-metrics
        image: nicolaka/netshoot:v0.9
        env:
          - name: METRICS_ENDPOINT
            valueFrom:
              secretKeyRef:
                name: services-secret
                key: METRICS_ENDPOINT
        command: [ 'sh', '-c', "until curl $METRICS_ENDPOINT; do echo waiting for VictoriaMetrics service...; sleep 2; done" ]

      - name: check-tritonserver
        image: nicolaka/netshoot:v0.9
        env:
          - name: TRITON_INFERENCE_ENDPOINT
            valueFrom:
              secretKeyRef:
                name: services-secret
                key: TRITON_INFERENCE_ENDPOINT
        command: [ 'sh', '-c', "until nc -zv $(echo $TRITON_INFERENCE_ENDPOINT | sed 's/:.*//') 8001; do echo waiting for Triton Inference Server; sleep 2; done" ]

      containers:
      - name: "eyeq-tracker-{{ $.Values.global.configuration.site_id }}-{{ .name }}"
        image: "{{ $.Values.global.docker_registry }}/eyeq-tracker:{{ $.Values.containers.eyeq_tracker.image.tag }}"
        securityContext:
          runAsUser: 1000
          runAsGroup: 1000
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 3
          periodSeconds: 5
        envFrom:
          - configMapRef:
              name: service-context
          - secretRef:
              name: services-secret
        env:
          - name : CAMERA_ID
            value : "{{ .name }}"
          - name : THING_NAME
            value : "eyeq-tracker-{{ $.Values.global.configuration.site_id }}-{{ .name }}"
          - name : IOT_ENDPOINT
            valueFrom:
              secretKeyRef:
                name: aws-iot-credentials
                key: IOT_ENDPOINT
          - name: LOGURU_LEVEL
            value: {{ $.Values.containers.eyeq_tracker.log_level | default $.Values.global.log_level }}
        volumeMounts:
          - name: localtime
            mountPath: /etc/localtime
            readOnly: true
          - name: timezone
            mountPath: /etc/timezone
            readOnly: true
          - name: runfiles
            mountPath: /opt/run_files
          - name: logs
            mountPath: /opt/logs
          - name: credentials
            mountPath: /opt/keys/
            readOnly: true
      volumes:
        - name: credentials
          secret:
            secretName: "eyeq-tracker-{{ $.Values.global.configuration.site_id }}"
        - name: localtime
          hostPath:
            path: /etc/localtime
        - name: timezone
          hostPath:
            path: /etc/timezone
        - name: runfiles
          hostPath:
            path: /opt/eyeq/run_files
        - name: logs
          hostPath:
            path: /opt/eyeq/logs

      imagePullSecrets:
        {{- range $.Values.global.imagePullSecrets }}
        - name: {{ . }}
        {{- end }}

      nodeSelector:
        kubernetes.io/hostname: {{ $.Values.global.hostname }}
{{- end }}
{{- end }}
