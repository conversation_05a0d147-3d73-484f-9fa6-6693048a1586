{{- if $.Values.containers.mosaic.enabled }}
---
  # Source: eyecue/templates/mosaic/k8s-eyeq-mosaic.yaml
  apiVersion: apps/v1
  kind: Deployment
  metadata:
    name: eyecue-mosaic-recorder
    namespace: {{ include "eyecue.namespace" . }}
  spec:
    selector:
      matchLabels:
        run: eyecue-mosaic-recorder
    template:
      metadata:
        labels:
          run: eyecue-mosaic-recorder
        annotations:
          checksum/config: {{ include "eyecue.services-context-hash"  (dict "rootContext" .) }}
          checksum/secret: {{ include "eyecue.services-secret-hash"  (dict "rootContext" .) }}
          checksum/minio-secret: {{ include "eyecue.minio-secret-hash"  (dict "rootContext" .) }}
      spec:
        containers:
        - name: eyecue-mosaic-recorder
          image: "{{ $.Values.global.docker_registry }}/eyeq-mosaic:{{ $.Values.containers.mosaic.recorder.image.tag }}"

          {{- with .Values.configuration.resources.mosaic_recorder }}
          resources:
            {{- toYaml . | nindent 12 }}
          {{- end }}

          envFrom:
            - configMapRef:
                name: service-context
            - secretRef:
                name: services-secret
          env:
            - name: ENVIRONMENT_TYPE
              value: production

            - name : MINIO_AWS_URL_ENDPOINT
              valueFrom:
                secretKeyRef:
                  name: minio-secret
                  key: AWS_ENDPOINT_URL

            - name : MINIO_AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: minio-secret
                  key: AWS_ACCESS_KEY_ID

            - name : MINIO_AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: minio-secret
                  key: AWS_SECRET_ACCESS_KEY

            - name : AWS_DEFAULT_REGION
              valueFrom:
                secretKeyRef:
                  name: aws-eyeq-mosaic-credentials
                  key: MOSAIC_S3_AWS_DEFAULT_REGION

            - name: site_id
              valueFrom:
                secretKeyRef:
                  name: aws-eyeq-mosaic-credentials
                  key: SITE_ID

            - name: dashboard_username
              valueFrom:
                secretKeyRef:
                  name: aws-eyeq-mosaic-credentials
                  key: MOSAIC_DASHBOARD_USERNAME

            - name: AUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: aws-eyeq-mosaic-credentials
                  key: AUTH_CLIENT_ID

            - name: AUTH_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: aws-eyeq-mosaic-credentials
                  key: AUTH_CLIENT_SECRET

            - name: dashboard_password
              valueFrom:
                secretKeyRef:
                  name: aws-eyeq-mosaic-credentials
                  key: MOSAIC_DASHBOARD_PASSWORD

            - name: "DASHBOARD_URL"
              value : "{{ $.Values.containers.mosaic.dashboard_url }}"

            - name: "dashboard_version"
              value : "{{ $.Values.containers.mosaic.dashboard_version }}"


            - name: "dashboard_url"
              value : "{{ $.Values.containers.mosaic.dashboard_url }}"


            - name: "minio_bucket"
              value : "{{ $.Values.containers.mosaic.minio_bucket_name }}"

            - name : "DASHBOARD_TAB_NAME"
              value : "{{ $.Values.containers.mosaic.dashboard_tab }}"

            - name : "AUTH_METHOD"
              value : "{{ $.Values.containers.mosaic.AUTH_METHOD }}"

            - name: LOGURU_LEVEL
              value:  {{ $.Values.containers.mosaic.log_level | default $.Values.global.log_level }}

            - name : "AUTH_AUDIENCE"
              value : "{{ $.Values.containers.mosaic.AUTH_AUDIENCE }}"

            - name: LOG_PATH
              value: /logs/mosaic-recorder.log

          volumeMounts:
            - name: localtime
              mountPath: /etc/localtime
              readOnly: true

            - name: timezone
              mountPath: /etc/timezone
              readOnly: true

            - name: logs
              mountPath: /logs

        volumes:
          - name: localtime
            hostPath:
              path: /etc/localtime

          - name: timezone
            hostPath:
              path: /etc/timezone

          - name: logs
            hostPath:
              path: /opt/eyeq/logs

        imagePullSecrets:
        - name: aws-registry-001

        nodeSelector:
          kubernetes.io/hostname: {{ $.Values.global.hostname }}

{{- end }}
