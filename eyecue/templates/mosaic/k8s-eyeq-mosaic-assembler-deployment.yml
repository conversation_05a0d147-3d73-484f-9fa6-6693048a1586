{{- if $.Values.containers.mosaic.enabled }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: "eyecue-mosaic-assembler"
  labels:
    app: "eyecue-mosaic-assembler"
  namespace: {{ include "eyecue.namespace" . }}

spec:
  selector:
    matchLabels:
      app: "eyecue-mosaic-assembler"

  template:
    metadata:
      labels:
        app: "eyecue-mosaic-assembler"
      annotations:
        checksum/config: {{ include "eyecue.services-context-hash"  (dict "rootContext" .) }}
        checksum/secret: {{ include "eyecue.services-secret-hash"  (dict "rootContext" .) }}
        checksum/minio-secret: {{ include "eyecue.minio-secret-hash"  (dict "rootContext" .) }}
    spec:
      containers:
      - name: "eyecue-mosaic-assembler"
        image: "{{ $.Values.global.docker_registry }}/eyecue-assembler:{{ $.Values.containers.mosaic.assembler.image.tag }}"
        envFrom:
          - configMapRef:
              name: service-context
          - secretRef:
              name: services-secret

        env:
          - name: MINIO_AWS_ACCESS_KEY_ID
            valueFrom:
              secretKeyRef:
                name: minio-secret
                key: AWS_ACCESS_KEY_ID

          - name: MINIO_AWS_SECRET_ACCESS_KEY
            valueFrom:
              secretKeyRef:
                name: minio-secret
                key: AWS_SECRET_ACCESS_KEY

          - name: MINIO_AWS_URL_ENDPOINT
            valueFrom:
              secretKeyRef:
                name: minio-secret
                key: AWS_ENDPOINT_URL

          - name: AWS_ACCESS_KEY_ID
            valueFrom:
              secretKeyRef:
                name: aws-eyeq-mosaic-credentials
                key: MOSAIC_S3_AWS_ACCESS_KEY_ID

          - name: AWS_SECRET_ACCESS_KEY
            valueFrom:
              secretKeyRef:
                name: aws-eyeq-mosaic-credentials
                key: MOSAIC_S3_AWS_SECRET_ACCESS_KEY

          - name: AWS_DEFAULT_REGION
            valueFrom:
              secretKeyRef:
                name: aws-eyeq-mosaic-credentials
                key: MOSAIC_S3_AWS_DEFAULT_REGION

          - name: SLACK_CHANNEL_ID
            valueFrom:
              secretKeyRef:
                name: aws-eyeq-mosaic-credentials
                key: SLACK_CHANNEL_ID

          - name: SLACK_TOKEN
            valueFrom:
              secretKeyRef:
                name: aws-eyeq-mosaic-credentials
                key: SLACK_TOKEN

          - name: SLACK_WEBHOOK_URL
            value: {{ $.Values.containers.mosaic.slack_url }}

          - name: "MINIO_BUCKET_NAME"
            value : "{{ $.Values.containers.mosaic.minio_bucket_name }}"

          - name : "AWS_BUCKET_NAME"
            value : "{{ $.Values.containers.mosaic.bucket }}"

          - name: LOGURU_LEVEL
            value:  {{ $.Values.containers.mosaic.log_level | default $.Values.global.log_level }}

          - name: LOG_PATH
            value: /logs/mosaic-assembler.log
        volumeMounts:
          - name: localtime
            mountPath: /etc/localtime
            readOnly: true

          - name: timezone
            mountPath: /etc/timezone
            readOnly: true

          - name: sources
            mountPath: /root/dashboards/

          - name: logs
            mountPath: /logs

      volumes:

        - name: localtime
          hostPath:
            path: /etc/localtime

        - name: timezone
          hostPath:
            path: /etc/timezone

        - name: sources
          hostPath:
            path: /media/fingermark/storage/dashboards/

        - name: logs
          hostPath:
            path: /opt/eyeq/logs

      nodeSelector:
        kubernetes.io/hostname: {{ $.Values.global.hostname }}

{{- end }}
