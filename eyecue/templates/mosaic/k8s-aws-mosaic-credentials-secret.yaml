{{- if .Values.containers.mosaic.enabled }}
---
apiVersion: v1
kind: Secret
metadata:
  name: aws-eyeq-mosaic-credentials
  namespace: {{ include "eyecue.namespace" . }}
data:
  MOSAIC_S3_AWS_ACCESS_KEY_ID: {{ $.Values.credentials.mosaic.aws_access_key_id }}
  MOSAIC_S3_AWS_SECRET_ACCESS_KEY: {{ $.Values.credentials.mosaic.aws_secret_access_key }}
  MOSAIC_S3_AWS_DEFAULT_REGION: {{ $.Values.credentials.mosaic.aws_default_region }}
  MOSAIC_DASHBOARD_USERNAME: {{ $.Values.containers.mosaic.dashboard_username | b64enc }}
  MOSAIC_DASHBOARD_PASSWORD: {{ $.Values.containers.mosaic.dashboard_password | b64enc }}
  SITE_ID: {{ $.Values.global.configuration.site_id | b64enc }}
  SLACK_CHANNEL_ID: {{ $.Values.containers.mosaic.slack_channel_id | b64enc }}
  SLACK_TOKEN: {{ $.Values.containers.mosaic.slack_token | b64enc }}
  AUTH_CLIENT_ID: {{ $.Values.containers.mosaic.AUTH_CLIENT_ID | b64enc }}
  AUTH_CLIENT_SECRET: {{ $.Values.containers.mosaic.AUTH_CLIENT_SECRET | b64enc }}
{{- end }}
