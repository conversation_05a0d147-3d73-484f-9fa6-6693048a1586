{{- if $.Values.containers.log_cleanup.enabled }}
---
apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: log-cleanup
spec:
  schedule: "0 0 * * *"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: cleanup
              image: alpine:latest
              command:
                [
                  "sh",
                  "-c",
                  "find /opt/eyeq/logs/* -type f -mtime +10 -delete;",
                ]
              volumeMounts:
                - name: log-volume
                  mountPath: /opt/eyeq/logs/
          restartPolicy: OnFailure
          volumes:
            - name: log-volume
              hostPath:
                path: /opt/eyeq/logs/
{{- end }}