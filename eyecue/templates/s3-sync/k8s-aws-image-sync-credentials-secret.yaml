{{- $namespace := include "eyecue.namespace" . }}

apiVersion: v1
kind: Secret
metadata:
  name: aws-image-sync-credentials
  namespace: {{ $namespace }}
data:
  AWS_ACCESS_KEY_ID:
    {{ .Values.credentials.image_sync_s3.aws_access_key_id }}
  AWS_SECRET_ACCESS_KEY:
    {{ .Values.credentials.image_sync_s3.aws_secret_access_key }}
  AWS_DEFAULT_REGION:
    {{ .Values.credentials.image_sync_s3.aws_default_region }}
  AWS_ROLE_S3_UPLOAD:
    {{ printf "arn:aws:iam::%s:role/%s" .Values.configuration.account_id "EyecueValidationToolImagesS3UploadRole" | b64enc }}
