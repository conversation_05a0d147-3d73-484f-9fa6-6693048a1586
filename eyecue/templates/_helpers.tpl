{{- define "eyecue.namespace" -}}
  {{ .Values.configuration.namespace | default (tpl .Values.global.namespace .) }}
{{- end -}}

{{- define "infra.namespace" -}}
  {{ .Values.configuration.infra_namespace | default "infra" }}
{{- end -}}

{{- define "eyecue.services-secret" -}}
  {{- $rootContext := .rootContext -}}
  {{- $context := $rootContext.Values.configuration.context -}}
---
apiVersion: v1
kind: Secret
metadata:
  name: services-secret
  namespace: {{ .namespace | default (include "eyecue.namespace" $rootContext) }}
stringData:
  RABBITMQ_URI: "{{ tpl $context.rabbitmq_uri $rootContext }}"
  REDIS_URI: "{{ tpl $context.redis_uri $rootContext }}"
  MQTT_URI: "{{ tpl $context.mqtt_uri $rootContext }}"
  MONGODB_URI: "{{ tpl $context.mongodb_uri $rootContext }}"
  METRICS_ENDPOINT: "{{ tpl $context.metrics_endpoint $rootContext }}"
  TRITON_INFERENCE_ENDPOINT: "{{ tpl $context.triton_endpoint $rootContext }}"
{{- end -}}

{{- define "eyecue.services-secret-hash" -}}
  {{ include "eyecue.services-secret" . | sha256sum }}
{{- end -}}

{{- define "eyecue.services-context" -}}
  {{- $rootContext := .rootContext -}}
  {{- $configuration := $rootContext.Values.configuration -}}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: service-context
  namespace: {{ .namespace | default (include "eyecue.namespace" $rootContext) }}
data:
  ENVIRONMENT: "{{ $configuration.env }}"
  SITE_ID: "{{ $rootContext.Values.global.configuration.site_id }}"
  CUSTOMER: "{{ $configuration.customer }}"
  LAUNCHDARKLY_KEY: "{{ $configuration.ld_key }}"
  AWS_ACCOUNT_ID: "{{ $configuration.account_id }}"
  AWS_REGION: "{{ $configuration.aws_region }}"
  # Keeping here for backwards compatibility
  ACCOUNT_ID: "{{ $configuration.account_id  }}"
  REGION: "{{ $configuration.aws_region }}"
{{- end -}}

{{- define "eyecue.services-context-hash" -}}
  {{ include "eyecue.services-context" . | sha256sum }}
{{- end -}}

{{- define "eyecue.minio-secret" -}}
  {{- $rootContext := .rootContext -}}
  {{- $context := $rootContext.Values.configuration.context -}}
  {{- $credentials := $rootContext.Values.credentials -}}
---
apiVersion: v1
kind: Secret
metadata:
  name: minio-secret
  namespace: {{ .namespace | default (include "eyecue.namespace" $rootContext) }}
stringData:
  AWS_ACCESS_KEY_ID: {{ $credentials.minio.username }}
  AWS_SECRET_ACCESS_KEY: {{ $credentials.minio.password }}
  AWS_ENDPOINT_URL: {{ tpl $context.minio_endpoint $rootContext }}

{{- end -}}

{{- define "eyecue.minio-secret-hash" -}}
  {{ include "eyecue.minio-secret" . | sha256sum }}
{{- end -}}
