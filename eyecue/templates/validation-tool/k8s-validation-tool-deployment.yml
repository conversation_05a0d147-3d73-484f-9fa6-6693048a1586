{{- if $.Values.containers.validation_tool.enabled }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: validation-tool
  labels:
    app: validation-tool

  namespace: {{ include "eyecue.namespace" . }}

spec:
  selector:
    matchLabels:
      app: validation-tool
  template:
    metadata:
      labels:
        app: validation-tool
      annotations:
        checksum/config: {{ include "eyecue.services-context-hash"  (dict "rootContext" .) }}
        checksum/secret: {{ include "eyecue.services-secret-hash"  (dict "rootContext" .) }}
        checksum/minio-secret: {{ include "eyecue.minio-secret-hash"  (dict "rootContext" .) }}
    spec:
      containers:
      - name: validation-tool-backend
        image: "{{ $.Values.global.docker_registry }}/validation-tool:{{ $.Values.containers.validation_tool.image.tag }}-backend"
        envFrom:
          - configMapRef:
              name: service-context
          - secretRef:
              name: services-secret
          - secretRef:
              name: aws-image-sync-credentials

        env:
          - name: site_id
            value: "{{ $.Values.global.configuration.site_id }}"

          - name: S3_BUCKET_NAME
            valueFrom:
              secretKeyRef:
                name: aws-s3-image-bucket-credentials
                key: S3_IMAGES_BUCKET

          - name: MINIO_ACCESS_KEY
            valueFrom:
              secretKeyRef:
                name: minio-secret
                key: AWS_ACCESS_KEY_ID

          - name: MINIO_SECRET_KEY
            valueFrom:
              secretKeyRef:
                name: minio-secret
                key: AWS_SECRET_ACCESS_KEY

          - name: MINIO_ENDPOINT
            valueFrom:
              secretKeyRef:
                name: minio-secret
                key: AWS_ENDPOINT_URL

          - name: MONGODB_NAME
            value: "dev"

          - name: S3_PREFIX
            value: "validation-tool-images"

          - name: BEST_SHOTS_BUCKET_NAME
            value: "best-shots"

          - name: VALIDATION_BUCKET_NAME
            value: "validations"

          - name: JWT_SECRET
            value: "55f16e4b623cb6812079b343c0c04205dacb75844a7bc160253b1a7f3562b9f1"

          - name: JWT_ALGORITHM
            value: "HS256"

          - name: JWT_ACCESS_TOKEN_EXPIRE_MINUTES
            value: "120"

        volumeMounts:
          - name: timezone
            mountPath: /etc/timezone
            readOnly: true

      - name: validation-tool-frontend
        image: "{{ $.Values.global.docker_registry }}/validation-tool:{{ $.Values.containers.validation_tool.image.tag }}-frontend"
        envFrom:
          - configMapRef:
              name: service-context
        env:
        - name: PUBLIC_INTERNAL_API_HOSTNAME
          value: "localhost"

        - name: PUBLIC_INTERNAL_API_PORT
          value: "80"

        - name: ORIGIN
          value: "http://{{ $.Values.global.configuration.site_id }}.eyeq.vpn:{{ .Values.containers.validation_tool.ports.frontend }}"

      volumes:
        - name: timezone
          hostPath:
            path: /etc/timezone

      imagePullSecrets:
        {{- range .Values.global.imagePullSecrets }}
        - name: {{ . }}
        {{- end }}

      nodeSelector:
        kubernetes.io/hostname: {{ .Values.global.hostname }}

{{- end }}
