{{- if $.Values.containers.validation_tool.enabled }}
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: validation-tool
  name: validation-tool-nodeport-svc
  namespace: {{ include "eyecue.namespace" . }}

spec:
  ports:
    - name: frontend
      port: 3000
      protocol: TCP
      targetPort: 3000
      nodePort: {{ .Values.containers.validation_tool.ports.frontend }}
  selector:
    app: validation-tool
  type: NodePort
{{- end }}
