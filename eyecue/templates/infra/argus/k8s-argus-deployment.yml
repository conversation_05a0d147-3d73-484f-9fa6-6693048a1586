{{- if $.Values.configuration.infra_deploy }}
{{- if $.Values.containers.eyecue_argus.enabled }}

---
apiVersion: apps/v1
kind: Deployment

metadata:
  name: eyecue-argus
  labels:
    app: "eyecue-argus"
  namespace: {{ include "infra.namespace" . }}

spec:
  selector:
    matchLabels:
      app: "eyecue-argus"

  template:
    metadata:
      labels:
        app: "eyecue-argus"
    spec:
      hostNetwork: true
      containers:
      - name: eyecue-argus
        image: "{{ $.Values.global.docker_registry }}/eyecue-argus:{{ $.Values.containers.eyecue_argus.image.tag }}"

        {{- with .Values.configuration.resources.eyecue_argus }}
        resources:
          {{- toYaml . | nindent 10 }}
        {{- end }}

        envFrom:
        - secretRef:
            name: argus-secrets

        env:
        - name: RESOURCE_DISCOVERY_SECONDS
          value: "{{ $.Values.containers.eyecue_argus.resource_discovery_seconds }}"

        - name: ARGUS_SENTRY_IO_CONNECTION_URL
          value: "{{ $.Values.containers.eyecue_argus.sentry_io_connection_url }}"

        - name: SENTRY_RELEASE
          value: "{{ $.Values.containers.eyecue_argus.sentry_release }}"

        - name: ENVIRONMENT
          value: "{{ $.Values.containers.eyecue_argus.environment }}"

        - name: NMAP_PORTS_TO_SCAN
          value: "{{ $.Values.containers.eyecue_argus.nmap_ports_to_scan }}"

        - name: NMAP_TIMING
          value: "{{ $.Values.containers.eyecue_argus.nmap_timing }}"

        - name: NMAP_TIMEOUT
          value: "{{ $.Values.containers.eyecue_argus.nmap_timeout }}"

        - name: NETWORK_INTERFACES
          value: "{{ $.Values.containers.eyecue_argus.network_interfaces }}"

        - name: REAL_HOSTNAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName

      imagePullSecrets:
        {{- range .Values.global.imagePullSecrets }}
        - name: {{ . }}
        {{- end }}

      nodeSelector:
        kubernetes.io/hostname: {{ .Values.global.hostname }}

{{- end }}
{{- end }}
