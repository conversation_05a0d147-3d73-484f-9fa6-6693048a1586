{{- if .Values.containers.pre_puller.enabled }}
{{- if gt (len .Values.containers.pre_puller.images) 0 }}

---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: pre-puller
  namespace: {{ include "infra.namespace" . }}
spec:
  selector:
    matchLabels:
      name: pre-puller
  template:
    metadata:
      labels:
        name: pre-puller
    spec:
      initContainers:
        {{- range $index, $image := .Values.containers.pre_puller.images }}
        - name: pre-puller-{{ $index }}
          image: {{ $image }}
          command: ["sh", "-c", "'true'"]
        {{- end }}

      # Use the pause container to ensure the Pod goes into a `Running` phase
      # but doesn't take up resource on the cluster
      containers:
        - name: pause
          image: docker.io/rancher/pause:{{ .Values.containers.pre_puller.image.tag }}
          resources:
            limits:
              cpu: 1m
              memory: 8Mi
            requests:
              cpu: 1m
              memory: 8Mi

{{- end }}
{{- end }}
