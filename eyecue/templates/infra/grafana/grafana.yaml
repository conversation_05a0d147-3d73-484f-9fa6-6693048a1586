{{- if eq $.Values.configuration.infra_deploy true }}
{{- $namespace := include "infra.namespace" . }}
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: grafana-pv-{{ $namespace }}
  namespace: {{ $namespace }}
spec:
  accessModes:
    - ReadWriteOnce
  capacity:
    storage: 1Gi
  hostPath:
    path: /media/fingermark/storage/grafana/{{ $namespace }}/
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: grafana-pvc-{{ $namespace }}
  namespace: {{ $namespace }}
  labels:
    app: grafana-storage-claim-{{ $namespace }}

spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
  volumeName: grafana-pv-{{ $namespace }}  # Name of the PersistentVolume to bind to

---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: grafana
  name: grafana
  namespace: {{ $namespace }}

spec:
  selector:
    matchLabels:
      app: grafana
  template:
    metadata:
      labels:
        app: grafana
    spec:
      # securityContext:
      #   runAsUser: 472
      #   fsGroup: 472
      containers:
        - name: grafana
          image: "{{ $.Values.global.docker_registry }}/eyecue-grafana:{{ $.Values.containers.eyeq_grafana.image.tag }}"
          imagePullPolicy: IfNotPresent
          env:
            - name: GF_SECURITY_ADMIN_USER
              value: admin
            - name: GF_SECURITY_ADMIN_PASSWORD
              value: password
          ports:
            - containerPort: 3000
              name: http-grafana
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /robots.txt
              port: 3000
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 2
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 10
            successThreshold: 1
            tcpSocket:
              port: 3000
            timeoutSeconds: 1
          resources:
            requests:
              cpu: 250m
              memory: 750Mi
          volumeMounts:
            - mountPath: /var/lib/grafana
              name: grafana-pv-{{ $namespace }}
              readOnly: false
            - mountPath: /etc/grafana/provisioning/datasources
              name: grafana-datasources
              readOnly: false
      initContainers:
        - name: take-data-dir-ownership
          securityContext:
            privileged: true
          image: alpine:3
          # Give `grafana` user (id 472) permissions a mounted volume
          # https://github.com/grafana/grafana-docker/blob/master/Dockerfile
          command:
          - chown
          - -R
          - 472:472
          - /var/lib/grafana
          volumeMounts:
            - mountPath: /var/lib/grafana
              name: grafana-pv-{{ $namespace }}
              readOnly: false
      volumes:
        - name: grafana-pv-{{ $namespace }}
          persistentVolumeClaim:
            claimName: grafana-pvc-{{ $namespace }}
        - name: grafana-datasources
          configMap:
              defaultMode: 420
              name: grafana-datasources
      imagePullSecrets:
        {{- range .Values.global.imagePullSecrets }}
        - name: {{ . }}
        {{- end }}
---
apiVersion: v1
kind: Service
metadata:
  name: grafana
  namespace: {{ $namespace }}
spec:
  ports:
    - name: port-3000
      port: 3000
      targetPort: 3000
      protocol: TCP
      nodePort: {{ .Values.configuration.ports.grafana_port }}
  selector:
    app: grafana
  sessionAffinity: None
  type: NodePort
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-datasources
  namespace: {{ $namespace }}
data:
  prometheus.yaml: |-
    {
        "apiVersion": 1,
        "datasources": [
            {
               "access":"proxy",
                "editable": true,
                "name": "victoriametrics",
                "orgId": 1,
                "type": "prometheus",
                "url": "http://victoria-metrics-svc.{{ $namespace }}.svc.cluster.local:8428",
                "version": 1,
                "isDefault": true,
                "updateIntervalSeconds" : 10
            }
        ]
    }
{{- end }}
