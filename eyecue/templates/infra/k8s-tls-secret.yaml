{{- if eq .Values.configuration.infra_deploy true }}
---
apiVersion: v1
kind: Secret
type: Opaque
metadata:
  name: eyeq-self-sign-ssl-credentials
  namespace: {{ include "infra.namespace" . }}
data:
  ca.crt: {{ $.Values.credentials.self_signed_ssl_certificate.ca_cert }}
  tls.key: {{ $.Values.credentials.self_signed_ssl_certificate.server_key }}
  tls.crt: {{ $.Values.credentials.self_signed_ssl_certificate.server_cert }}

{{- end }}
