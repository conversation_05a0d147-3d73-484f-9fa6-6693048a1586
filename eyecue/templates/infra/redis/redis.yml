{{- if eq $.Values.configuration.infra_deploy true }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: {{ include "infra.namespace" . }}
  name: redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9121"
      labels:
        app: redis
    spec:
      containers:
        - name: redis
          image: redis:latest
          {{- with .Values.configuration.resources.redis }}
          resources:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          args:
            - "--rdb-save-incremental-fsync"
            - "no"
            - "--stop-writes-on-bgsave-error"
            - "no"
            - "--save"
            - ""


{{- /*
If the environment is prod then we don't expose the redis service to the outside world.
*/}}

{{- if eq $.Values.configuration.env "prod" }}
---
apiVersion: v1
kind: Service
metadata:
  name: "redis"
  namespace: {{ include "infra.namespace" . }}
spec:
  selector:
    app: "redis"
  ports:
  - name: port-6379
    port: 6379
    targetPort: 6379
{{- end }}

{{- if eq $.Values.configuration.env "qa" }}
---
apiVersion: v1
kind: Service
metadata:
  name: "redis"
  namespace: {{ include "infra.namespace" . }}
spec:
  type: NodePort
  selector:
    app: "redis"
  ports:
  - name: port-6379
    port: 6379
    targetPort: 6379
    nodePort: {{ .Values.configuration.ports.redis_port }}
{{- end }}
{{- end }}
