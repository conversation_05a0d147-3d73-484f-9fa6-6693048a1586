{{- if .Values.containers.camera_metrics_exporter.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: camera-metrics-exporter
  namespace: {{ .Values.containers.camera_metrics_exporter.namespace }}
  labels:
    app.kubernetes.io/service: camera-metrics-exporter
spec:
  type: ClusterIP
  ports:
    - port: 8080
      targetPort: 8080
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: camera-metrics-exporter

{{- end }}
