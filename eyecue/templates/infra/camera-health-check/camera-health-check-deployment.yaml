{{- if .Values.containers.camera_metrics_exporter.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: camera-metrics-exporter
  namespace: {{ .Values.containers.camera_metrics_exporter.namespace }}
  labels:
    app.kubernetes.io/name: camera-metrics-exporter
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: camera-metrics-exporter
  template:
    metadata:
      labels:
        app.kubernetes.io/name: camera-metrics-exporter
    spec:
      containers:
        - name: health-check
          image: "{{ .Values.global.docker_registry }}/eyecue-camera-metrics-exporter:{{ .Values.containers.camera_metrics_exporter.image.tag }}"
          imagePullPolicy: IfNotPresent
          {{- with .Values.containers.camera_metrics_exporter.env }}
          env:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          envFrom:
            - secretRef:
                name: camera-metrics-exporter
          ports:
            - containerPort: 8080
              protocol: TCP
              name: http
          resources:
            limits:
              memory: 125Mi
            requests:
              cpu: 10m
              memory: 125Mi
          livenessProbe:
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 30
            periodSeconds: 20
          readinessProbe:
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 10
            periodSeconds: 5
{{- end }}
