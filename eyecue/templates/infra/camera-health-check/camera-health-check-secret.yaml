{{- if .Values.containers.camera_metrics_exporter.enabled }}
apiVersion: v1
kind: Secret
metadata:
  name: camera-metrics-exporter
  namespace: {{ .Values.containers.camera_metrics_exporter.namespace }}
stringData:
  AWS_ACCESS_KEY_ID: {{ .Values.credentials.camera_metrics_exporter.aws_access_key_id }}
  AWS_DEFAULT_REGION: {{ .Values.credentials.camera_metrics_exporter.aws_default_region }}
  AWS_SECRET_ACCESS_KEY: {{ .Values.credentials.camera_metrics_exporter.aws_secret_access_key }}
{{- end }}
