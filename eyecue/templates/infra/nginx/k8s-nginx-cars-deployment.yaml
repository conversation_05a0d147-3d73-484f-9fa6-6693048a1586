{{- if $.Values.configuration.infra_deploy }}
{{- if $.Values.containers.nginx.enabled }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-cars
  labels:
    app: "nginx-cars"
  namespace: {{ include "infra.namespace" . }}
spec:
  selector:
    matchLabels:
      app: "nginx-cars"
  strategy:
    type: Recreate

  template:
    metadata:
      labels:
        app: "nginx-cars"
    spec:
      hostNetwork: true
      containers:
      - name: nginx-cars
        ports:
        - containerPort: {{ $.Values.configuration.ports.nginx_port }}
        image: "{{ $.Values.global.docker_registry }}/nginx:{{ $.Values.containers.nginx.image.tag }}"
        {{- if $.Values.credentials.self_signed_ssl_certificate }}
        env:
          - name: cert_sn
            value: "{{ $.Values.credentials.self_signed_ssl_certificate.cert_sn }}"
        {{- end }}

        volumeMounts:
          {{- if $.Values.credentials.self_signed_ssl_certificate }}
          - name: ssl-certificates
            mountPath: "/etc/ssl/self_signed_ssl/"
            readOnly: true
          {{- end }}

          - name: eyecue-images
            mountPath: /usr/share/images/
            readOnly: true

          - name: cache
            mountPath: /data/nginx/cache

      volumes:
        {{- if $.Values.credentials.self_signed_ssl_certificate }}
        - name: ssl-certificates
          secret:
            secretName: eyeq-self-sign-ssl-credentials
            items:
              - key: ca.crt
                path: ca/ca.pem
              - key: tls.key
                path: server/key.pem
              - key: tls.crt
                path: server/cert.pem
        {{- end }}
        - name: eyecue-images
          hostPath:
            path: /media/fingermark/storage/nginx

        - name: cache
          hostPath:
            path: /media/fingermark/storage/dashboard-cache

      imagePullSecrets:
        {{- range .Values.global.imagePullSecrets }}
        - name: {{ . }}
        {{- end }}

      nodeSelector:
        kubernetes.io/hostname: {{ .Values.global.hostname }}

{{- end }}
{{- end }}
