{{- if eq .Values.configuration.infra_deploy true }}
{{- $namespace := include "infra.namespace" . }}
{{- $modelsBucket := .Values.containers.triton.models_bucket }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: triton
  name: triton
  namespace: {{ $namespace }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: triton
  strategy: {}
  template:
    metadata:
      labels:
        app: triton
    spec:
      initContainers:
        - name: wait-for-gpu-version
          image: "nvcr.io/nvidia/tritonserver:{{ tpl $.Values.containers.triton.image.tag $ }}"
          imagePullPolicy: IfNotPresent
          volumeMounts:
            - name: data-volume
              mountPath: /mnt/weights/
          command:
            - "/bin/sh"
            - "-c"
            - |
              while [ ! -f /mnt/weights/gpu_version.txt ]; do
                echo "Waiting for gpu_version.txt to be created by eyecue-weights-sync... sleeping for 5 seconds"
                sleep 5
              done

              MODEL_REPO_PATH=$(cat /mnt/weights/gpu_version.txt)

              cat <<EOF
              gpu_version.txt found, with the following content:
                ${MODEL_REPO_PATH}

              If triton fails to start, it is most likely for one of the following reasons:
                1. The GPU of this server is not supported and therefore the model repository path
                   at s3://{{ $modelsBucket }}/${MODEL_REPO_PATH}/models/ does not exist.
                2. Eyecue weights sync is still downloading the models. You can check the
                   logs of the eyecue-weights-sync pod to see the status of the download.
                     kubectl logs -n {{ $namespace }} -l app=eyecue-weights-sync

                   You should see one of the following messages:
                     - Sync completed successfully with no changes!
                     - Sync completed successfully, triton should be reloading the weights shortly.

              EOF

      containers:
        - name: triton
          image: "nvcr.io/nvidia/tritonserver:{{ tpl $.Values.containers.triton.image.tag $ }}"
          imagePullPolicy: IfNotPresent

          {{- with .Values.configuration.resources.triton }}
          resources:
            {{- toYaml . | nindent 12 }}
          {{- end }}

          ports:
            - containerPort: 8000
              name: http
            - containerPort: 8001
              name: grpc
            - containerPort: 8002
              name: metrics

          {{- if .Values.containers.triton.probes.enabled }}
          livenessProbe:
            httpGet:
              path: /v2/health/live
              port: 8000
            initialDelaySeconds: 20
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3

          readinessProbe:
            httpGet:
              path: /v2/health/ready
              port: 8000
            initialDelaySeconds: 20
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3

          startupProbe:
            httpGet:
              path: /v2/health/ready
              port: 8000
            failureThreshold: 30
            periodSeconds: 5
            timeoutSeconds: 5
          {{- end }}

          command:
            - "/bin/sh"
            - "-c"
            - |
              MODEL_REPO_PATH=$(cat /mnt/weights/gpu_version.txt)

              tritonserver \
                {{- /* The following configs makes triton check the weights every 5 minutes for changes and auto-reloads them */}}
                --model-control-mode=poll --repository-poll-secs=300 \
                {{- /* Add the args from the values.yaml */}}
                {{- range $arg := $.Values.containers.triton.args }}
                  {{ $arg }} \
                {{- end }}
                --model-repository=/mnt/weights/{{ $modelsBucket }}/${MODEL_REPO_PATH}/models/

          volumeMounts:
            - name: data-volume
              mountPath: /mnt/weights/

      volumes:
        - name: data-volume
          persistentVolumeClaim:
            claimName: triton-weights-pvc

      nodeSelector:
        kubernetes.io/hostname: {{ $.Values.global.hostname }}

---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: triton
  name: triton
  namespace: {{ $namespace }}
spec:
  type: ClusterIP
  ports:
    - name: http
      port: 8000
      targetPort: 8000
    - name: grpc
      port: 8001
      targetPort: 8001
    - name: metrics
      port: 8002
      targetPort: 8002
  selector:
    app: triton

---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: eyecue-weights-sync
  name: eyecue-weights-sync
  namespace: {{ $namespace }}
spec:
  type: ClusterIP
  ports:
    - name: http
      port: 5000
      targetPort: 5000
  selector:
    app: eyecue-weights-sync

---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: eyecue-weights-sync
  name: eyecue-weights-sync
  namespace: {{ $namespace }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: eyecue-weights-sync
  strategy: {}
  template:
    metadata:
      labels:
        app: eyecue-weights-sync
      annotations:
        checksum/config: {{ include "eyecue.services-context-hash"  (dict "rootContext" .) }}
        checksum/secret: {{ include "eyecue.services-secret-hash"  (dict "rootContext" .) }}
    spec:
      initContainers:
        - name: gpu-version
          image: "nvcr.io/nvidia/tritonserver:{{ tpl $.Values.containers.triton.image.tag $ }}"
          imagePullPolicy: IfNotPresent
          volumeMounts:
            - name: data-volume
              mountPath: /mnt/weights/
          command:
            - "/bin/sh"
            - "-c"
            - |
              {{- /*
                Get the GPU model name from nvidia-smi.

                Some examples include:
                  - NVIDIA GeForce GTX 1080 Ti
                  - NVIDIA RTX A2000 12GB
                  - Quadro RTX 4000
              */}}
              GPU_NAME=$(
                nvidia-smi \
                  --query-gpu=gpu_name \
                  --format=csv,noheader
              )

              {{- /*
                Do a best guess conversion of the GPU_NAME to our model repository path formats.

                The steps are as follows:
                  - Remove the NVIDIA prefix
                  - Remove the GeForce prefix
                  - Remove the Quadro prefix
                  - Remove the [0-9]+GB suffix
                  - Convert the remaining string to lowercase
                  - Replace spaces with dashes
              */}}
              GPU_MODEL=$(
                echo ${GPU_NAME} \
                | sed 's/NVIDIA //' \
                | sed 's/GeForce //' \
                | sed 's/Quadro //' \
                | sed 's/2000/4000/' \
                | sed 's/ [0-9]\+GB//' \
                | tr '[:upper:]' '[:lower:]' \
                | tr ' ' '-'
              )

              MODEL_REPO_PATH="triton/${GPU_MODEL}-{{ $.Values.containers.triton.image.tag }}"
              S3_BUCKET_PATH="s3://{{ $modelsBucket }}/${MODEL_REPO_PATH}/models/"

              cat <<EOF
              GPU model name:        ${GPU_NAME}
              GPU model path:        ${GPU_MODEL}
              S3 bucket path:        ${S3_BUCKET_PATH}
              Model repository path: ${MODEL_REPO_PATH}

              EOF

              {{- /*
                Write the model repository path to a file in the mounted volume.
                This will be used by the triton server to load the correct model.
              */}}

              echo ${MODEL_REPO_PATH} > /mnt/weights/gpu_version.txt
              echo "Model repository path written to /mnt/weights/gpu_version.txt"

      containers:
      - name: eyecue-weights-sync
        image: "{{ $.Values.global.docker_registry }}/eyecue-weights-sync:{{ $.Values.containers.eyecue_weights_sync.image.tag }}"
        ports:
          - containerPort: 5000
        envFrom:
          - configMapRef:
              name: service-context
          - secretRef:
              name: services-secret
          - secretRef:
              name: triton-aws-sync-credentials
        env:
          - name: ENVIRONMENT_TYPE
            value: production
          - name: MODELS_BUCKET
            value: {{ $.Values.containers.triton.models_bucket }}
          - name: LOGURU_LEVEL
            value:  {{ $.Values.containers.eyecue_weights_sync.log_level | default $.Values.global.log_level }}

        volumeMounts:
        - name: data-volume
          mountPath: /mnt/weights/

      volumes:
        - name: data-volume
          persistentVolumeClaim:
            claimName: triton-weights-pvc

      imagePullSecrets:
      - name: aws-registry-001

      nodeSelector:
        kubernetes.io/hostname: {{ $.Values.global.hostname }}

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: triton-weights-pvc
  namespace: {{ $namespace }}
  labels:
    app: eyecue-weights-sync
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 7Gi
  volumeName: triton-weights-pv

---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: triton-weights-pv
  namespace: {{ $namespace }}
  labels:
    app: eyecue-weights-sync
spec:
  capacity:
    storage: 7Gi
  accessModes:
    - ReadWriteOnce
  hostPath:
    path: /media/fingermark/storage/triton-weights/

{{- end }}
