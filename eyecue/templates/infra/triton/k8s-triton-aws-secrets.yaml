{{- if eq $.Values.configuration.infra_deploy true }}
---
apiVersion: v1
kind: Secret
metadata:
  name: triton-aws-sync-credentials
  namespace:  {{ include "infra.namespace" . }}
data:
  AWS_ACCESS_KEY_ID: {{ .Values.credentials.eyecue_weights_sync.aws_access_key_id | b64enc }}
  AWS_SECRET_ACCESS_KEY: {{ .Values.credentials.eyecue_weights_sync.aws_secret_access_key | b64enc}}
  AWS_DEFAULT_REGION: {{ .Values.credentials.eyecue_weights_sync.aws_default_region | b64enc}}

{{- end }}
