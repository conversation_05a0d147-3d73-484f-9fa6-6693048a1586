{{- if and $.Values.configuration.infra_deploy $.Values.containers.vmagent.enabled }}
{{- $namespace := include "infra.namespace" . }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: vmagent
  namespace: {{ $namespace }}
spec:
  selector:
    matchLabels:
      app: vmagent
  template:
    metadata:
      labels:
        app: vmagent
      annotations:
        checksum/config: {{ include (print ($.Template.Name | dir) "/vmagent-configmap.yaml") . | sha256sum }}
    spec:
      containers:
      - name: vmagent
        image: {{ $.Values.global.docker_registry }}/vmagent:{{ $.Values.containers.vmagent.image.tag }}
        volumeMounts:
        - name: prometheus-config
          mountPath: /etc/prometheus/
        - name: vmalert-relabel-configs
          mountPath: /etc/vmalert-relabel-configs/
        env:
        - name: HOSTNAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        args:
        # we can't scape the url below because shell doesn't expand the env variable
        {{- if eq $.Values.configuration.remote_metrics.enabled true }}
        - --remoteWrite.url={{ $.Values.configuration.remote_metrics.url }}
        - --remoteWrite.urlRelabelConfig=/etc/vmalert-relabel-configs/remote_write_relabel_configs.yml
        - --remoteWrite.basicAuth.username={{ $.Values.configuration.remote_metrics.username }}
        - --remoteWrite.basicAuth.password={{ $.Values.configuration.remote_metrics.password }}
        - --remoteWrite.tlsInsecureSkipVerify=true
        - --remoteWrite.label=site_id={{ $.Values.global.configuration.site_id }}
        - --remoteWrite.flushInterval=5s
        - --remoteWrite.sendTimeout=30s

        # temporarily keep old metrics endpoint
        - --remoteWrite.url=https://ec2-18-208-145-252.compute-1.amazonaws.com:8427/api/v1/write
        - --remoteWrite.basicAuth.username=local-single-node
        - --remoteWrite.basicAuth.password=my-houst
        {{- end }}
        - --promscrape.config=/etc/prometheus/prometheus.yml
        - --remoteWrite.url=http://victoria-metrics-svc.{{ $namespace }}.svc.cluster.local:8428/api/v1/write

        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 200m
            memory: 256Mi
      volumes:
      - name: prometheus-config
        configMap:
          name: prometheus-configmap
      - name: vmalert-relabel-configs
        configMap:
          name: vmalert-relabel-configmap
      imagePullSecrets:
      - name: aws-registry-001
      nodeSelector:
        kubernetes.io/hostname: {{ .Values.global.hostname }}
---
apiVersion: v1
kind: Service
metadata:
  name: vmagent-svc
  namespace: {{ $namespace }}
spec:
  selector:
    app: vmagent
  sessionAffinity: None
  ports:
    - name: http
      port: 8429
      targetPort: 8429
{{- end }}
