{{- if and $.Values.configuration.infra_deploy $.Values.containers.victoriametrics.enabled }}
{{- $namespace := include "infra.namespace" . }}
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: victoria-metrics-pv-{{ $namespace }}
  namespace: {{ $namespace }}
spec:
  capacity:
    storage: 1Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  hostPath:
    path: /media/fingermark/storage/victoria-metrics/metrics/{{ $namespace }}/
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: victoria-metrics-pvc-{{ $namespace }}
  namespace: {{ $namespace }}
  labels:
    app: victoria-metrics-storage-claim-{{ $namespace }}

spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
  volumeName: victoria-metrics-pv-{{ $namespace }}  # Name of the PersistentVolume to bind to
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: victoria-metrics
  name: victoria-metrics
  namespace: {{ $namespace }}

spec:
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app: victoria-metrics
  template:
    metadata:
      labels:
        app: victoria-metrics
    spec:
      containers:
      - name: victoria-metrics
        image: {{ $.Values.global.docker_registry }}/victoria-metrics:{{ $.Values.containers.victoriametrics.image.tag }}
        resources:
            requests:
              cpu: 250m
              memory: 750Mi
        args:
          - "--storageDataPath=/storage"
          - "--graphiteListenAddr=:2003"
          - "--opentsdbListenAddr=:4242"
          - "--httpListenAddr=:8428"
          - "--influxListenAddr=:8089"
          - "--vmalert.proxyURL=http://vmalert.{{ $namespace }}.svc.cluster.local:8880"
          - -retentionPeriod=1.5

        env:
          - name: VMALERT_PROXY_URL
            value: "http://vmalert.{{ $namespace }}.svc.cluster.local:8880"
        volumeMounts:
        - name: vmdata
          mountPath: /storage
      #   readinessProbe:
      #     httpGet:
      #       path: /-/ready
      #       port: http
      #     initialDelaySeconds: 10
      #     periodSeconds: 10
      #   livenessProbe:
      #     httpGet:
      #       path: /-/healthy
      #       port: http
      #     initialDelaySeconds: 20
      #     periodSeconds: 20
      volumes:
      - name: vmdata
        persistentVolumeClaim:
          claimName: victoria-metrics-pvc-{{ $namespace }}
      imagePullSecrets:
      - name: aws-registry-001
---
apiVersion: v1
kind: Service
metadata:
  name: victoria-metrics-svc
  namespace: {{ $namespace }}
spec:
  selector:
    app: victoria-metrics
  sessionAffinity: None
  ports:
    - name: graphite
      port: 2003
      targetPort: 2003
    - name: opentsdb
      port: 4242
      targetPort: 4242
    - name: http
      port: 8428
      targetPort: 8428
    - name: influx
      port: 8089
      targetPort: 8089


{{- end }}
