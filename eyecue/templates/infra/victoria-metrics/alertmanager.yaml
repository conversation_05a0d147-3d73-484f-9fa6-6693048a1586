{{- if and $.Values.configuration.infra_deploy $.Values.containers.alertmanager.enabled }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: alertmanager
  namespace: {{ include "infra.namespace" . }}
spec:
  selector:
    matchLabels:
      app: alertmanager
  replicas: 1
  template:
    metadata:
      labels:
        app: alertmanager
    spec:
      containers:
        - name: alertmanager
          image: {{ $.Values.global.docker_registry }}/alertmanager:{{ $.Values.containers.alertmanager.image.tag }}
          volumeMounts:
            - name: localtime
              mountPath: /etc/localtime
              readOnly: true

            - name: timezone
              mountPath: /etc/timezone
              readOnly: true
        - name: alertmanager-sidecar
          image: {{ $.Values.global.docker_registry }}/alertmanager:{{ $.Values.containers.vmalert.image.tag }}-silencer
          envFrom:
            - configMapRef:
                name: service-context
          env:
            - name: ALERTMANAGER_URL
              value: "http://alertmanager.{{ include "infra.namespace" . }}.svc.cluster.local:9093"
          volumeMounts:
            - name: localtime
              mountPath: /etc/localtime
              readOnly: true

            - name: timezone
              mountPath: /etc/timezone
              readOnly: true

      volumes:
        - name: localtime
          hostPath:
            path: /etc/localtime

        - name: timezone
          hostPath:
            path: /etc/timezone

      imagePullSecrets:
      - name: aws-registry-001
      nodeSelector:
        kubernetes.io/hostname: {{ .Values.global.hostname }}
---
apiVersion: v1
kind: Service
metadata:
  name: alertmanager
  namespace: {{ include "infra.namespace" . }}
spec:
  selector:
    app: alertmanager
  ports:
  - name: web
    port: 9093
    targetPort: 9093
{{- end }}
