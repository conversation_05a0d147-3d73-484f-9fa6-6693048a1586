{{- if and $.Values.configuration.infra_deploy $.Values.containers.vmagent.enabled }}
{{- $namespace := include "infra.namespace" . }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-configmap
  namespace: {{ $namespace }}
data:
  prometheus.yml: |
    global:
      scrape_interval: 30s

    scrape_configs:
      - job_name: 'node-exporter'
        static_configs:
          - targets:
            - 'eyecue-node-exporter.monitoring.svc.cluster.local:9100'
      - job_name: 'kube-state-metrics'
        static_configs:
          - targets:
            - 'eyecue-kube-state-metrics.monitoring.svc.cluster.local:8080'
      - job_name: 'camera-metrics-exporter'
        scrape_interval: 60s
        static_configs:
          - targets:
            - 'camera-metrics-exporter.monitoring.svc.cluster.local:8080'
      - job_name: 'argocd'
        static_configs:
          - targets:
            - 'argocd-application-controller-metrics.argocd.svc.cluster.local:8082'
            - 'argocd-server-metrics.argocd.svc.cluster.local:8083'
            - 'argocd-repo-server-metrics.argocd.svc.cluster.local:8084'
      - job_name: triton-inference-server
        static_configs:
          - targets: ['http://triton.{{ $namespace }}.svc.cluster.local:8002/metrics']
      - job_name: kubedns
        static_configs:
          - targets: ['kube-dns.kube-system.svc.cluster.local:9153']
      - job_name: rabbitmq
        static_configs:
          - targets: ['eyecue-rabbitmq.infra.svc.cluster.local:9419']
      - job_name: mongodb
        static_configs:
          - targets: ['eyecue-mongodb-metrics.infra.svc.cluster.local:9216']
      - job_name: minio
        static_configs:
          - targets: ['eyecue-minio.infra.svc.cluster.local:9000']
        metrics_path: /minio/v2/metrics/cluster
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: vmalert-relabel-configmap
  namespace: {{ $namespace }}
data:
  # the first regex drops any metrics that do not match the prefix eyeq or eyecue
  remote_write_relabel_configs.yml: |
    - source_labels: [__name__]
      regex: '^(eyecue|eyeq|departures|aggregated|validation|moonfire|argocd|node|kube|coredns)\w+'
      action: keep
{{- end }}
