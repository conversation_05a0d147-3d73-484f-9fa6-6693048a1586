{{- if and $.Values.configuration.infra_deploy $.Values.containers.vmalert.enabled }}
{{- $namespace := include "infra.namespace" . }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: vmalert
  namespace: {{ $namespace }}
spec:
  selector:
    matchLabels:
      app: vmalert
  replicas: 1
  template:
    metadata:
      labels:
        app: vmalert
    spec:
      containers:
        - name: vmalert
          image: {{ $.Values.global.docker_registry }}/vmalert:{{ $.Values.containers.vmalert.image.tag }}

          args:
            - "--datasource.url=http://victoria-metrics-svc.{{ $namespace }}.svc.cluster.local:8428/"
            - "--remoteRead.url=http://victoria-metrics-svc.{{ $namespace }}.svc.cluster.local:8428/"
            - "--remoteWrite.url=http://victoria-metrics-svc.{{ $namespace }}.svc.cluster.local:8428/"
            - "--notifier.url=http://alertmanager.{{ $namespace }}.svc.cluster.local:9093/"
            - "--rule=/etc/alerts/*.yml"
            - "--external.url=http://{{ .Values.global.hostname }}.eyeq.vpn:{{ .Values.configuration.ports.grafana_port }}/"
          env:
            - name: HOSTNAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
      imagePullSecrets:
      - name: aws-registry-001
      nodeSelector:
        kubernetes.io/hostname: {{ .Values.global.hostname }}
  strategy:
    type: Recreate
---
apiVersion: v1
kind: Service
metadata:
  name: vmalert
  namespace: {{ $namespace }}
spec:
  selector:
    app: vmalert
  ports:
    - name: http
      port: 8880
      targetPort: 8880
{{- end }}
