{{- if $.Values.containers.camera_config_changer.enabled }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: camera-config-changer
  labels:
    app: camera-config-changer
  namespace: {{ include "eyecue.namespace" . }}
spec:
  selector:
    matchLabels:
      app: camera-config-changer
  template:
    metadata:
      labels:
        app: camera-config-changer
      annotations:
        checksum/config: {{ include "eyecue.services-context-hash"  (dict "rootContext" .) }}
        checksum/secret: {{ include "eyecue.services-secret-hash"  (dict "rootContext" .) }}
    spec:
      containers:
      - name: camera-config-changer
        image: {{ $.Values.global.docker_registry }}/camera-config-changer:{{ $.Values.containers.camera_config_changer.image.tag }}

        envFrom:
          - configMapRef:
              name: service-context
          - secretRef:
              name: services-secret

        env:
          - name: LOG_PATH
            value: /logs/camera-config-changer.log

        volumeMounts:
          - name: timezone
            mountPath: /etc/timezone
            readOnly: true

          - name: logs
            mountPath: /logs/

      volumes:
        - name: timezone
          hostPath:
            path: /etc/timezone

        - name: logs
          hostPath:
            path: /opt/eyeq/logs

      imagePullSecrets:
        {{- range .Values.global.imagePullSecrets }}
        - name: {{ . }}
        {{- end }}

      nodeSelector:
        kubernetes.io/hostname: {{ .Values.global.hostname }}
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: camera-config-changer
  name: camera-config-changer-svc
  namespace: {{ include "eyecue.namespace" . }}

spec:
  ports:
    - port: 80
      protocol: TCP
      targetPort: 80
      nodePort: 30111
  selector:
    app: camera-config-changer
  type: NodePort
{{- end }}
