{{- if $.Values.containers.roi_suggestor.enabled }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: roi-suggestor
  labels:
    app: roi-suggestor

  namespace: {{ include "eyecue.namespace" . }}

spec:
  selector:
    matchLabels:
      app: roi-suggestor-jobs
  template:
    metadata:
      labels:
        app: roi-suggestor-jobs
      annotations:
        checksum/config: {{ include "eyecue.services-context-hash"  (dict "rootContext" .) }}
        checksum/secret: {{ include "eyecue.services-secret-hash"  (dict "rootContext" .) }}
        checksum/minio-secret: {{ include "eyecue.minio-secret-hash"  (dict "rootContext" .) }}
    spec:
      containers:
      - name: roi-suggestor-jobs
        image: {{ $.Values.global.docker_registry }}/roi-suggestor-jobs:{{ $.Values.containers.roi_suggestor.jobs.image.tag }}
        # logs are written every 10 mins, so this liveness probe kills the container if it freezes.
        livenessProbe:
          exec:
            command:
            - sh
            - -c
            - |
              find /logs/roi-suggestor-job.log -mmin -10 | grep . || exit 1
          failureThreshold: 3
          initialDelaySeconds: 60
          periodSeconds: 120
          successThreshold: 1
          timeoutSeconds: 1

        {{- with $.Values.configuration.resources.roi_suggestor }}
        resources:
          {{- toYaml . | nindent 12 }}
        {{- end }}

        envFrom:
          - configMapRef:
              name: service-context
          - secretRef:
              name: services-secret

        env:
          - name: LOG_PATH
            value: /logs/roi-suggestor-job.log

          - name: LOGURU_LEVEL
            value: {{ $.Values.containers.roi_suggestor.jobs.log_level | default $.Values.global.log_level }}

          - name : S3_IMAGES_BUCKET
            valueFrom:
              secretKeyRef:
                name: aws-s3-image-bucket-credentials
                key: S3_IMAGES_BUCKET
          - name: AWS_S3_ENDPOINT_URL
            valueFrom:
              secretKeyRef:
                key: AWS_ENDPOINT_URL
                name: minio-secret
          - name: AWS_ACCESS_KEY_ID
            valueFrom:
              secretKeyRef:
                key: AWS_ACCESS_KEY_ID
                name: minio-secret
          - name: AWS_SECRET_ACCESS_KEY
            valueFrom:
              secretKeyRef:
                key: AWS_SECRET_ACCESS_KEY
                name: minio-secret
          - name: AWS_DEFAULT_REGION
            valueFrom:
              secretKeyRef:
                key: MOSAIC_S3_AWS_DEFAULT_REGION
                name: aws-eyeq-mosaic-credentials
          - name : BUCKET_NAME
            value: "$(S3_IMAGES_BUCKET)/heatmap-generation/"

          - name: SITE_ID
            value: {{ $.Values.global.configuration.site_id }}

        volumeMounts:
          - name: logs
            mountPath: /logs/

      volumes:

        - name: logs
          hostPath:
            path: /opt/eyeq/logs

      imagePullSecrets:
        {{- range .Values.global.imagePullSecrets }}
        - name: {{ . }}
        {{- end }}

      nodeSelector:
        kubernetes.io/hostname: {{ $.Values.global.hostname }}

{{- end }}
