
{{- if $.Values.containers.queue_zone.enabled }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: queue-zone
  labels:
    app: queue-zone

  namespace: {{ include "eyecue.namespace" . }}

spec:
  selector:
    matchLabels:
      app: queue-zone
  template:
    metadata:
      labels:
        app: queue-zone
      annotations:
        checksum/config: {{ include "eyecue.services-context-hash"  (dict "rootContext" .) }}
        checksum/secret: {{ include "eyecue.services-secret-hash"  (dict "rootContext" .) }}
    spec:
      containers:
      - name: queue-zone
        image: {{ $.Values.global.docker_registry }}/queue-zone:{{ $.Values.containers.queue_zone.image.tag }}
        envFrom:
          - configMapRef:
              name: service-context
          - secretRef:
              name: services-secret

        env:
          - name: LOG_PATH
            value: /logs/queue-zone.log

        volumeMounts:
          - name: localtime
            mountPath: /etc/localtime
            readOnly: true

          - name: timezone
            mountPath: /etc/timezone
            readOnly: true

          - name: logs
            mountPath: /logs/

      volumes:
        - name: localtime
          hostPath:
            path: /etc/localtime

        - name: timezone
          hostPath:
            path: /etc/timezone

        - name: logs
          hostPath:
            path: /opt/eyeq/logs

      imagePullSecrets:
        {{- range .Values.global.imagePullSecrets }}
        - name: {{ . }}
        {{- end }}

      nodeSelector:
        kubernetes.io/hostname: {{ .Values.global.hostname }}

{{- end }}
