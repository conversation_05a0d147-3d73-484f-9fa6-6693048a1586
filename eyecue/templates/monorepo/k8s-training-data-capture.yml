{{- if $.Values.containers.training_data_capture.enabled }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: training-data-capture
  labels:
    app: training-data-capture

  namespace: {{ include "eyecue.namespace" . }}

spec:
  selector:
    matchLabels:
      app: training-data-capture
  template:
    metadata:
      labels:
        app: training-data-capture
      annotations:
        checksum/config: {{ include "eyecue.services-context-hash"  (dict "rootContext" .) }}
        checksum/secret: {{ include "eyecue.services-secret-hash"  (dict "rootContext" .) }}
    spec:
      containers:
      - name: training-data-capture
        image: {{ $.Values.global.docker_registry }}/training-data-capture:{{ $.Values.containers.training_data_capture.image.tag }}
        envFrom:
          - configMapRef:
              name: service-context
          - secretRef:
              name: services-secret

        env:
          - name: LOG_PATH
            value: /logs/training-data-capture.log

          - name : S3_IMAGES_BUCKET
            valueFrom:
              secretKeyRef:
                name: aws-s3-image-bucket-credentials
                key: S3_IMAGES_BUCKET

          - name: AWS_ACCESS_KEY_ID
            valueFrom:
              secretKeyRef:
                name: aws-image-sync-credentials
                key: AWS_ACCESS_KEY_ID

          - name: AWS_SECRET_ACCESS_KEY
            valueFrom:
              secretKeyRef:
                name: aws-image-sync-credentials
                key: AWS_SECRET_ACCESS_KEY

          - name: AWS_DEFAULT_REGION
            valueFrom:
              secretKeyRef:
                name: aws-image-sync-credentials
                key: AWS_DEFAULT_REGION

          - name : BUCKET_NAME
            value: "$(S3_IMAGES_BUCKET)/object-detection/"

        volumeMounts:

          - name: logs
            mountPath: /logs/

      volumes:

        - name: logs
          hostPath:
            path: /opt/eyeq/logs

      imagePullSecrets:
        {{- range .Values.global.imagePullSecrets }}
        - name: {{ . }}
        {{- end }}

      nodeSelector:
        kubernetes.io/hostname: {{ $.Values.global.hostname }}

{{- end }}
