{{- if $.Values.containers.edge_metadata_gatekeeper.enabled }}
---
apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: edge-metadata-gatekeeper
  labels:
    app: edge-metadata-gatekeeper
  namespace: {{ include "eyecue.namespace" . }}

spec:
  schedule: {{ $.Values.containers.edge_metadata_gatekeeper.schedule }}
  startingDeadlineSeconds: 300
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            app: edge-metadata-gatekeeper

        spec:
          hostNetwork: true
          containers:
          - name: edge-metadata-gatekeeper
            image: {{ $.Values.global.docker_registry }}/edge-metadata-gatekeeper:{{ $.Values.containers.edge_metadata_gatekeeper.image.tag }}
            securityContext:
              privileged: true

            envFrom:
              - configMapRef:
                  name: service-context
              - secretRef:
                  name: aws-docker-credentials

            env:
              - name: LOG_PATH
                value: /logs/edge-metadata-gatekeeper.log

            volumeMounts:
              - name: timezone
                mountPath: /etc/timezone
                readOnly: true

              - name: logs
                mountPath: /logs/

              - name: sys-dmi
                mountPath: /mnt/sys
                readOnly: true

              - name: nvidia
                mountPath: /mnt/nvidia
                readOnly: true

              - name: device-dir
                mountPath: /dev

          restartPolicy: Never
          volumes:
            - name: timezone
              hostPath:
                path: /etc/timezone

            - name: logs
              hostPath:
                path: /opt/eyeq/logs

            - name: sys-dmi
              hostPath:
                path: /sys/class/dmi/id

            - name: nvidia
              hostPath:
                path: /proc/driver/nvidia

            - name: device-dir
              hostPath:
                path: /dev

          imagePullSecrets:
            {{- range .Values.global.imagePullSecrets }}
            - name: {{ . }}
            {{- end }}

          nodeSelector:
            kubernetes.io/hostname: {{ .Values.global.hostname }}

{{- end }}
