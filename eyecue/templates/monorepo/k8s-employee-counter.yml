{{- if $.Values.containers.employee_counter.enabled }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: employee-counter
  labels:
    app: employee-counter

  namespace: {{ include "eyecue.namespace" . }}

spec:
  selector:
    matchLabels:
      app: employee-counter
  template:
    metadata:
      labels:
        app: employee-counter
      annotations:
        checksum/config: {{ include "eyecue.services-context-hash"  (dict "rootContext" .) }}
        checksum/secret: {{ include "eyecue.services-secret-hash"  (dict "rootContext" .) }}
    spec:
      containers:
      - name: employee-counter
        image: {{ $.Values.global.docker_registry }}/employee_counter:{{ $.Values.containers.employee_counter.image.tag }}
        envFrom:
          - configMapRef:
              name: service-context
          - secretRef:
              name: services-secret

        env:
          - name: LOG_PATH
            value: /logs/employee-counter.log

          - name: LOGURU_LEVEL
            value: {{ $.Values.containers.employee_counter.log_level | default $.Values.global.log_level }}

        volumeMounts:
          - name: localtime
            mountPath: /etc/localtime
            readOnly: true

          - name: timezone
            mountPath: /etc/timezone
            readOnly: true

          - name: logs
            mountPath: /logs/

      volumes:
        - name: localtime
          hostPath:
            path: /etc/localtime

        - name: timezone
          hostPath:
            path: /etc/timezone

        - name: logs
          hostPath:
            path: /opt/eyeq/logs

      imagePullSecrets:
        {{- range .Values.global.imagePullSecrets }}
        - name: {{ . }}
        {{- end }}

      nodeSelector:
        kubernetes.io/hostname: {{ .Values.global.hostname }}

{{- end }}
