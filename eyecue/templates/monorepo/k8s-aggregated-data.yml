{{- if $.Values.containers.aggregated_data.enabled }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: aggregated-data
  labels:
    app: aggregated-data

  namespace: {{ include "eyecue.namespace" . }}

spec:
  selector:
    matchLabels:
      app: aggregated-data
  template:
    metadata:
      labels:
        app: aggregated-data
      annotations:
        checksum/config: {{ include "eyecue.services-context-hash"  (dict "rootContext" .) }}
        checksum/secret: {{ include "eyecue.services-secret-hash"  (dict "rootContext" .) }}
    spec:
      containers:
      - name: aggregated-data
        image: {{ $.Values.global.docker_registry }}/aggregated-data:{{ $.Values.containers.aggregated_data.image.tag }}
        envFrom:
          - configMapRef:
              name: service-context
          - secretRef:
              name: services-secret
        env:
          - name: LOG_PATH
            value: /logs/aggregated-data.log
          - name: LOGURU_LEVEL
            value: {{ $.Values.containers.aggregated_data.log_level | default $.Values.global.log_level }}

        volumeMounts:
          - name: localtime
            mountPath: /etc/localtime
            readOnly: true

          - name: timezone
            mountPath: /etc/timezone
            readOnly: true

          - name: logs
            mountPath: /logs/

      volumes:
        - name: localtime
          hostPath:
            path: /etc/localtime

        - name: timezone
          hostPath:
            path: /etc/timezone

        - name: logs
          hostPath:
            path: /opt/eyeq/logs

      imagePullSecrets:
        {{- range .Values.global.imagePullSecrets }}
        - name: {{ . }}
        {{- end }}

      nodeSelector:
        kubernetes.io/hostname: {{ .Values.global.hostname }}

{{- end }}
