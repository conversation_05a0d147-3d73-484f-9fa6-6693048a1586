{{- if $.Values.containers.departures_mongodb.enabled }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: departures-mongodb
  labels:
    app: departures-mongodb

  namespace: {{ include "eyecue.namespace" . }}

spec:
  selector:
    matchLabels:
      app: departures-mongodb
  template:
    metadata:
      labels:
        app: departures-mongodb
      annotations:
        checksum/config: {{ include "eyecue.services-context-hash"  (dict "rootContext" .) }}
        checksum/secret: {{ include "eyecue.services-secret-hash"  (dict "rootContext" .) }}
    spec:
      containers:
      - name: departures-mongodb
        image: {{ $.Values.global.docker_registry }}/departures-mongodb:{{ $.Values.containers.departures_mongodb.image.tag }}
        envFrom:
          - configMapRef:
              name: service-context
          - secretRef:
              name: services-secret

        env:
          - name: LOG_PATH
            value: /logs/departures-mongodb.log

        volumeMounts:
          - name: localtime
            mountPath: /etc/localtime
            readOnly: true

          - name: timezone
            mountPath: /etc/timezone
            readOnly: true

          - name: logs
            mountPath: /logs/

      volumes:
        - name: localtime
          hostPath:
            path: /etc/localtime

        - name: timezone
          hostPath:
            path: /etc/timezone

        - name: logs
          hostPath:
            path: /opt/eyeq/logs

      imagePullSecrets:
        {{- range .Values.global.imagePullSecrets }}
        - name: {{ . }}
        {{- end }}

      nodeSelector:
        kubernetes.io/hostname: {{ .Values.global.hostname }}

{{- end }}
