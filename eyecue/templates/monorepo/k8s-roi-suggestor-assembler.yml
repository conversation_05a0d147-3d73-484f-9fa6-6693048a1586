{{- if .Values.containers.roi_suggestor.enabled }}
{{- $namespace := include "eyecue.namespace" . }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: roi-suggestor-assembler
  labels:
    app: roi-suggestor-assembler

  namespace: {{ $namespace }}

spec:
  selector:
    matchLabels:
      app: roi-suggestor-assembler
  template:
    metadata:
      labels:
        app: roi-suggestor-assembler
      annotations:
        checksum/config: {{ include "eyecue.services-context-hash"  (dict "rootContext" .) }}
        checksum/secret: {{ include "eyecue.services-secret-hash"  (dict "rootContext" .) }}
        checksum/minio-secret: {{ include "eyecue.minio-secret-hash"  (dict "rootContext" .) }}
    spec:
      containers:
      - name: roi-suggestor-assembler
        image: {{ $.Values.global.docker_registry }}/roi-suggestor-assembler:{{ $.Values.containers.roi_suggestor.assembler.image.tag }}

        {{- with $.Values.configuration.resources.roi_suggestor_assembler }}
        resources:
          {{- toYaml . | nindent 12 }}
        {{- end }}

        envFrom:
          - configMapRef:
              name: service-context
          - secretRef:
              name: services-secret
        env:
            # AWS S3 stuff
            - name : S3_IMAGES_BUCKET
              valueFrom:
                secretKeyRef:
                  name: aws-s3-image-bucket-credentials
                  key: S3_IMAGES_BUCKET

            # - name: AWS_URL_ENDPOINT
            #   valueFrom:
            #     secretKeyRef:
            #       key: AWS_ENDPOINT_URL
            #       name: minio-secret

            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: aws-image-sync-credentials
                  key: AWS_ACCESS_KEY_ID

            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: aws-image-sync-credentials
                  key: AWS_SECRET_ACCESS_KEY

            - name: AWS_DEFAULT_REGION
              valueFrom:
                secretKeyRef:
                  name: aws-image-sync-credentials
                  key: AWS_DEFAULT_REGION
            - name : AWS_BUCKET_NAME
              value: "$(S3_IMAGES_BUCKET)"
            # AWS SQS stuff
            - name: AWS_SQS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: roi-suggestor-sqs-secret
                  key: AWS_ACCESS_KEY_ID

            - name: AWS_SQS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: roi-suggestor-sqs-secret
                  key: AWS_SECRET_ACCESS_KEY

            # Minio stuff
            - name: MINIO_AWS_URL_ENDPOINT
              valueFrom:
                secretKeyRef:
                  name: minio-secret
                  key: AWS_ENDPOINT_URL

            - name: MINIO_AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: minio-secret
                  key: AWS_ACCESS_KEY_ID

            - name: MINIO_AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: minio-secret
                  key: AWS_SECRET_ACCESS_KEY

            - name: SLACK_WEBHOOK_URL
              value: {{ $.Values.containers.roi_suggestor.slack_url }}
            - name : MINIO_BUCKET_NAME
              value: {{ $.Values.containers.roi_suggestor.minio_bucket_name }}
            # Misc
            - name: LOG_PATH
              value: /logs/roi-suggestor-assembler.log
            - name: LOGURU_LEVEL
              value: {{ $.Values.containers.roi_suggestor.assembler.log_level | default $.Values.global.log_level }}
            # Infra
        volumeMounts:
          - name: logs
            mountPath: /logs/

      volumes:

        - name: logs
          hostPath:
            path: /opt/eyeq/logs

      imagePullSecrets:
        {{- range .Values.global.imagePullSecrets }}
        - name: {{ . }}
        {{- end }}

      nodeSelector:
        kubernetes.io/hostname: {{ $.Values.global.hostname }}

---
apiVersion: v1
kind: Secret
metadata:
  name: roi-suggestor-sqs-secret
  namespace: {{ $namespace }}
data:
  AWS_ACCESS_KEY_ID: {{ $.Values.credentials.roi_suggestor_sqs.aws_access_key_id }}
  AWS_SECRET_ACCESS_KEY: {{ $.Values.credentials.roi_suggestor_sqs.aws_secret_access_key }}

{{- end }}
