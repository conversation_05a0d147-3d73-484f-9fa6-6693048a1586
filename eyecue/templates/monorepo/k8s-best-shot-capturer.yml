{{- if $.Values.containers.best_shot_capturer.enabled }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: best-shot-capturer
  labels:
    app: best-shot-capturer
  namespace: {{ include "eyecue.namespace" . }}

spec:
  selector:
    matchLabels:
      app: best-shot-capturer
  template:
    metadata:
      labels:
        app: best-shot-capturer
      annotations:
        checksum/config: {{ include "eyecue.services-context-hash"  (dict "rootContext" .) }}
        checksum/secret: {{ include "eyecue.services-secret-hash"  (dict "rootContext" .) }}
        checksum/minio-secret: {{ include "eyecue.minio-secret-hash"  (dict "rootContext" .) }}
    spec:
      containers:
      - name: best-shot-capturer
        image: "{{ $.Values.global.docker_registry }}/best-shot-capturer:{{ $.Values.containers.best_shot_capturer.image.tag }}"
        envFrom:
          - configMapRef:
              name: service-context
          - secretRef:
              name: services-secret

        env:
          - name: LOG_PATH
            value: /logs/best-shot-capturer.log

          - name: IMAGES_PATH
            value: /images/

          - name: site_id
            value: "{{ $.Values.global.configuration.site_id }}"

          - name: MINIO_AWS_ACCESS_KEY_ID
            valueFrom:
              secretKeyRef:
                name: minio-secret
                key: AWS_ACCESS_KEY_ID

          - name: MINIO_AWS_SECRET_ACCESS_KEY
            valueFrom:
              secretKeyRef:
                name: minio-secret
                key: AWS_SECRET_ACCESS_KEY

          - name: MINIO_AWS_URL_ENDPOINT
            valueFrom:
              secretKeyRef:
                name: minio-secret
                key: AWS_ENDPOINT_URL

          - name : S3_IMAGES_BUCKET
            valueFrom:
              secretKeyRef:
                name: aws-s3-image-bucket-credentials
                key: S3_IMAGES_BUCKET

          - name: AWS_ACCESS_KEY_ID
            valueFrom:
              secretKeyRef:
                name: aws-image-sync-credentials
                key: AWS_ACCESS_KEY_ID

          - name: AWS_SECRET_ACCESS_KEY
            valueFrom:
              secretKeyRef:
                name: aws-image-sync-credentials
                key: AWS_SECRET_ACCESS_KEY

          - name: AWS_DEFAULT_REGION
            valueFrom:
              secretKeyRef:
                name: aws-image-sync-credentials
                key: AWS_DEFAULT_REGION

          - name : S3_BUCKET_NAME
            value: "$(S3_IMAGES_BUCKET)/best-shot-saver/"

          - name: SITE_ID
            value: "{{ $.Values.global.configuration.site_id }}"

        volumeMounts:
          - name: localtime
            mountPath: /etc/localtime
            readOnly: true

          - name: timezone
            mountPath: /etc/timezone
            readOnly: true

          - name: logs
            mountPath: /logs/

          - name: images
            mountPath: /images/

      volumes:
        - name: localtime
          hostPath:
            path: /etc/localtime

        - name: timezone
          hostPath:
            path: /etc/timezone

        - name: logs
          hostPath:
            path: /opt/eyeq/logs

        - name: images
          hostPath:
            path: /media/fingermark/storage/best-shot-capturer/

      imagePullSecrets:
        {{- range .Values.global.imagePullSecrets }}
        - name: {{ . }}
        {{- end }}

      nodeSelector:
        kubernetes.io/hostname: {{ .Values.global.hostname }}

{{- end }}
