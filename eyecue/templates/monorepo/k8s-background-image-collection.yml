{{- if $.Values.containers.background_image_collection.enabled }}
{{- $cameras := $.Values.configuration.cameras | default $.Values.global.cameras }}
---
apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: background-image-collection
  namespace: {{ include "eyecue.namespace" . }}
spec:
  schedule: {{ $.Values.configuration.cron_schedules.background_image_collection | default "30 * * * *" }}
  startingDeadlineSeconds: {{.Values.containers.background_image_collection.starting_deadline_seconds| default 604800}}
  concurrencyPolicy: Replace
  jobTemplate:
    spec:
      completions: 1
      backoffLimit: 1
      # do not run for more than 1 day straight
      activeDeadlineSeconds: {{ $.Values.containers.background_image_collection.active_deadline_seconds }}
      template:
        metadata:
          labels:
            app: background-image-collection
          annotations:
            checksum/config: {{ include "eyecue.services-context-hash"  (dict "rootContext" .) }}
            checksum/secret: {{ include "eyecue.services-secret-hash"  (dict "rootContext" .) }}
        spec:
          containers:
            - name: background-image-collection
              image: {{ $.Values.global.docker_registry }}/background-image-collection:{{ $.Values.containers.background_image_collection.image.tag }}
              envFrom:
                - configMapRef:
                    name: service-context
                - secretRef:
                    name: services-secret
              env:
                - name: LOG_PATH
                  value: /logs/background-image-collection.log
                - name: S3_IMAGES_BUCKET
                  valueFrom:
                    secretKeyRef:
                      name: aws-s3-image-bucket-credentials
                      key: S3_IMAGES_BUCKET
                - name: BUCKET_NAME
                  value: "$(S3_IMAGES_BUCKET)/background_images"
                - name: AWS_ACCESS_KEY_ID
                  valueFrom:
                    secretKeyRef:
                      name: aws-image-sync-credentials
                      key: AWS_ACCESS_KEY_ID
                - name: AWS_SECRET_ACCESS_KEY
                  valueFrom:
                    secretKeyRef:
                      name: aws-image-sync-credentials
                      key: AWS_SECRET_ACCESS_KEY
                - name: AWS_DEFAULT_REGION
                  valueFrom:
                    secretKeyRef:
                      name: aws-image-sync-credentials
                      key: AWS_DEFAULT_REGION
                - name: CAMERAS
                  value: |-
                    {{- range $cameras }}
                    - {{ .name | quote }}
                    {{- end }}
              volumeMounts:
                - name: localtime
                  mountPath: /etc/localtime
                  readOnly: true
                - name: timezone
                  mountPath: /etc/timezone
                  readOnly: true
                - name: logs
                  mountPath: /logs/
          volumes:
            - name: localtime
              hostPath:
                path: /etc/localtime
            - name: timezone
              hostPath:
                path: /etc/timezone
            - name: logs
              hostPath:
                path: /opt/eyeq/logs
          imagePullSecrets:
            {{- range .Values.global.imagePullSecrets }}
            - name: {{ . }}
            {{- end }}

          nodeSelector:
            kubernetes.io/hostname: {{ .Values.global.hostname }}
          restartPolicy: OnFailure

{{- end }}
