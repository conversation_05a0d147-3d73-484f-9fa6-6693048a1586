{{- if $.Values.containers.camera_displacement.enabled }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: camera-displacement
  labels:
    app: camera-displacement
  namespace: {{ include "eyecue.namespace" . }}
spec:
  selector:
    matchLabels:
      app: camera-displacement
  template:
    metadata:
      labels:
        app: camera-displacement
      annotations:
        checksum/config: {{ include "eyecue.services-context-hash"  (dict "rootContext" .) }}
        checksum/secret: {{ include "eyecue.services-secret-hash"  (dict "rootContext" .) }}
    spec:
      containers:
      - name: camera-displacement
        image: {{ $.Values.global.docker_registry }}/camera-displacement:{{ $.Values.containers.camera_displacement.image.tag }}

        envFrom:
          - configMapRef:
              name: service-context
          - secretRef:
              name: services-secret

        env:
          # AWS S3 bucket configurations
          - name: S3_IMAGES_BUCKET_CAPTURED
            value: {{ $.Values.containers.camera_displacement.images_bucket_captured }}

          - name: S3_IMAGES_BUCKET_CONFIG
            value: {{ $.Values.containers.camera_displacement.images_bucket_config }}

          # AWS credentials
          - name: AWS_ACCESS_KEY_ID
            valueFrom:
              secretKeyRef:
                name: aws-image-sync-credentials
                key: AWS_ACCESS_KEY_ID

          - name: AWS_SECRET_ACCESS_KEY
            valueFrom:
              secretKeyRef:
                name: aws-image-sync-credentials
                key: AWS_SECRET_ACCESS_KEY

          - name: AWS_DEFAULT_REGION
            valueFrom:
              secretKeyRef:
                name: aws-image-sync-credentials
                key: AWS_DEFAULT_REGION

          - name: AWS_SQS_QUEUE_NAME
            value: {{ $.Values.containers.camera_displacement.aws_sqs_queue_name }}

          # Logging
          - name: LOG_PATH
            value: /logs/camera-displacement.log
          - name: LOGURU_LEVEL
            value: {{ $.Values.containers.camera_displacement.log_level | default $.Values.global.log_level }}
        volumeMounts:
          - name: timezone
            mountPath: /etc/timezone
            readOnly: true

          - name: logs
            mountPath: /logs/

      volumes:
        - name: timezone
          hostPath:
            path: /etc/timezone

        - name: logs
          hostPath:
            path: /opt/eyeq/logs

      imagePullSecrets:
        {{- range .Values.global.imagePullSecrets }}
        - name: {{ . }}
        {{- end }}

      nodeSelector:
        kubernetes.io/hostname: {{ .Values.global.hostname }}

---
apiVersion: v1
kind: Secret
metadata:
  name: camera-displacement-sqs-secret
  namespace: {{ include "eyecue.namespace" . }}

{{- end }}
